<?php

namespace App\Filament\Mc\Widgets;

use App\Models\McCustomer;
use App\Models\McDeposit;
use App\Models\McOrder;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class McStatWidget extends BaseWidget
{
	protected int | string | array $columnSpan = 4;

	protected static string $view = 'filament.admin.widgets.mc-stat-widget';

	public function getViewData(): array
	{
		// return Cache::remember('mcdashboard.order.summary', now()->addMinutes(5), function () {
			$now = now('Asia/Jakarta');
			$thisWeekStart = $now->copy()->startOfWeek();
			$thisWeekEnd   = $now->copy()->endOfWeek();
			$today         = $now->toDateString();

			$calcOrder = fn($query) => $query
				->selectRaw('SUM(total_order) AS totalOrder')
				->value('totalOrder') ?? 0;

			$calcProfit = fn($query) => $query
				->selectRaw('SUM((sell_rates - buy_rates) * amount) AS totalProfit')
				->value('totalProfit') ?? 0;


			$calcBalance = fn($query) => $query
				->selectRaw('SUM(balance) AS totalBalance')
				->value('totalBalance') ?? 0;

			$calcpayment = fn($query) => $query
				->selectRaw('SUM(amount) AS totalDeposit')
				->value('totalDeposit') ?? 0;

			$baseOrderQuery = fn() => McOrder::query()->where('order_code', 'NOT LIKE', '%CLS%');
			$baseCustomerQuery = fn() => McCustomer::query();
			$baseDepositQuery = fn() => McDeposit::query()->where('trx_code', 'NOT LIKE', '%CLS%');

			$orderThisWeek   = $calcOrder(($baseOrderQuery())
				->whereBetween('order_date', [$thisWeekStart, $thisWeekEnd]));
			$orderToday      = $calcOrder(($baseOrderQuery())->whereDate('order_date', $today));

			$profitThisWeek = $calcProfit(($baseOrderQuery())
				->whereBetween('order_date', [$thisWeekStart, $thisWeekEnd]));
			$profitToday = $calcProfit(($baseOrderQuery())->whereDate('order_date', $today));

			$balance   = $calcBalance($baseCustomerQuery());
			$paymentToday   = $calcpayment(($baseDepositQuery())->where('trx_type', 'incoming')
				->whereBetween('slip_date', [$thisWeekStart, $thisWeekEnd]));

			return [
				'deposit' => [
					'balance'  => $balance,
					'today' => $paymentToday,
				],
				'order' => [
					'week'  => $orderThisWeek,
					'weekNum' => $thisWeekStart->copy()->format('W'),
					'dayNum' => $now->copy()->format('d'),
					'today'   => $orderToday,
					'profitWeek' => $profitThisWeek,
					'profitToday' => $profitToday,
				],
			];
    	// });
	}


	public static function canAccess(): bool
	{
		return Auth::user()->hasAnyRole(['admin', 'Super Admin']);
	}
	public static function canView(): bool
	{
		return Auth::user()->hasAnyRole(['admin', 'Super Admin']);
	}
}
