<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\InvoiceResource\Pages;

use App\Models\BusinessType;
use App\Models\Company;
use App\Models\CompanyBank;
use App\Models\Currency;
use App\Models\Invoice;
use App\Models\MasterInvoiceDetail;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Forms\Components\{DatePicker, Fieldset, Group, Hidden, Placeholder, Repeater, RichEditor, Section, Select, Textarea, TextInput};
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Number;

class InvoiceResource extends Resource
{
	protected static ?string $model = Invoice::class;

	protected static ?string $navigationIcon = 'icon-file-ruled';
	protected static ?int $navigationSort = 1;
	protected static ?string $recordTitleAttribute = 'invoice_no';

	public static function form(Form $form): Form
	{
		return $form
			->columns(12)
			->schema([
				Fieldset::make('Order Information')
					->visible(function ($record) {
						if($record && $record->order)
						{
							return true;
						}
						return false;
					})
					->schema([
						Hidden::make('order_id'),
						Hidden::make('invoice_create_by')
							->default(Auth::user()->id),
						Hidden::make('parent_invoice_id'),
						Hidden::make('status'),
						Placeholder::make('OrderAmount')
							->label('Order')
							->inlineLabel()
							->columnSpan(1)
							->content(function ($record) {
								$orderAmount = $record->order->currency->symbol . ' ' . number_format($record->order_amount ?? 0, 2, ',', '.');
								return new HtmlString('<span class="text-primary-500 font-bold" target="_blank">' . $orderAmount . '</span>');
							}),
						Placeholder::make('Booking Fee')
							->columnSpan(1)
							->inlineLabel()
							->content(function ($record) {
								$bookingFee = $record->order->currency->symbol . ' ' . number_format($record->order->booking_fee ?? 0, 2, ',', '.');
								return new HtmlString('<span class="text-primary-500 font-bold" target="_blank">' . $bookingFee . '</span>');
							}),
						Placeholder::make('Total Order')
							->columnSpan(1)
							->inlineLabel()
							->content(function ($record) {
								$total = $record->order->currency->symbol . ' ' . number_format($record->order->total ?? 0, 2, ',', '.');
								return new HtmlString('<span class="text-primary-500 font-bold" target="_blank">' . $total . '</span>');
							}),
					])->hiddenOn('create')
					->columnSpanFull()->columns(3),
				Select::make('company_id')
					->label('Select Company')
					->options(fn ($get) => Company::whereNotNull('name')->where('name', '!=', '')->pluck('name', 'id'))
					->searchable()
					->reactive()
					->label('Company')
					->inlineLabel()
					->columnSpanFull()
					->required()
					->afterStateUpdated(function (callable $set, $state, callable $get) {
						static::templateSelect($set, $state);
						static::calculateFields($get, $set);
						static::updateInvoiceNumberPlaceholder($set, $state);
						$set('bankRepeaterSelect', []);
						$companyBank = CompanyBank::where('company_id', $state)->where('include_in_invoice', true)->get()->toArray();

						$includedBanks = CompanyBank::where('company_id', $state)
							->where('include_in_invoice', true)
							->pluck('id')
							->toArray();
						$set('bankRepeaterSelect', $includedBanks);
						$set('bankRepeater', $companyBank);
						$set('bankRepeater', $companyBank);
					}),
				Section::make('Basic Information')
					->columnSpan(6)
					->schema([
						TextInput::make('invoice_no')
							->placeholder(fn(callable $get) => $get('invoice_no_placeholder'))
							->live(onBlur: true)
							->inlineLabel()
							->required()
							->hintColor('danger')
							->maxLength(191)->hint(fn(callable $get) => $get('duplicate_warning'))
							->afterStateUpdated(function (callable $set, $state, callable $get, $record) {
								$isDuplicate = Invoice::where('invoice_no', $state)->first();
								$set('duplicate_warning', $isDuplicate ? 'Duplicate Invoice Number' : '');
								if ($isDuplicate) {
									$set('invoice_no', null);

									Notification::make()
										->title('Error')
										->danger()
										->body('Invoice has been used. Get another invoice number.')
										->send();
								}
							}),
						Select::make('currency_id')
							->label('Currency')
							->inlineLabel()
							->options(
								\App\Models\Currency::get()
									->mapWithKeys(fn($currency) => [
										$currency->id => "{$currency->symbol} - {$currency->name}"
									])
									->toArray()
							)
							->searchable()
							->required()
							->afterStateUpdated(function ($state, callable $set) {
								$currency = Currency::find($state);
								if ($currency) {
									$set('currency_symbol', $currency->symbol);
								}
							})
							->afterStateHydrated(function ($state, callable $set) {
								$currency = Currency::find($state);
								if ($currency) {
									$set('currency_symbol', $currency->symbol);
								}
							})
							->reactive(),
						DatePicker::make('invoice_date')
							->reactive()
							->inlineLabel()
							->default(today())
							->afterStateUpdated(function (callable $set, $state) {
								$maxDue = Carbon::parse($state)->addDays(14)->toDateString();
								$set('due_date', $maxDue);
							}),
						DatePicker::make('due_date')
							->minDate(fn($get) => $get('invoice_date'))
							->inlineLabel()
							->default(fn($get) => $get('invoice_date')
								? Carbon::parse($get('invoice_date'))->addDays(14)->toDateString()
								: today()->addDays(14)->toDateString())
							->minDate(fn($get) => $get('invoice_date')),
					]),

				Section::make('Client Information')
					->columnSpan(6)
					->schema([
						Select::make('client_id')
							->label('Select Client')
							->options(function () {
								return \App\Models\Company::whereNotNull('name')
									->where('name', '!=', '')
									->pluck('name', 'id');
							})
							->searchable()
							->reactive()
							->afterStateUpdated(function (callable $set, $state, callable $get) {
								$clientAddress = Company::find($state)?->address ?? null;

								$set('client_address', $clientAddress);
							})
							->required(),

						RichEditor::make('client_address')
							->extraInputAttributes(['style' => 'min-height: 5.5rem; max-height: 5.5vh; overflow-y: auto;'])
							->toolbarButtons([])
							->columnSpanFull(),
					]),
				Section::make('Invoice Details')
					->collapsible()
					->columns(12)
					->schema([
						Select::make('business_type')
							->inlineLabel()
							->label('Business Type')
							->reactive()
							->columnSpanFull()
							->searchable()
							->helperText('Set by Template. Overide to select another details by business type when available.')
							->options(
								BusinessType::get()
									->mapWithKeys(fn($business) => [
										$business->id => $business->name
									])
									->toArray()
							)
							->afterStateUpdated(function (callable $set, $state, callable $get) {
								static::businessTypeSelect($set, $state);
								static::calculateFields($get, $set);
							}),
						TableRepeater::make('invDetails')
							->hiddenLabel()
							->relationship('invoiceDetails')
							->emptyLabel('There are no item placed in the list.')
							->addActionLabel('Add Item')
							->columnSpanFull()
							->headers([
								Header::make('Description')->width('40%')->markAsRequired(),
								Header::make('Qty')->width('15%')->markAsRequired(),
								Header::make('Price')->width('20%')->markAsRequired(),
								Header::make('Total Price')->width('20%'),
							])
							->afterStateUpdated(function (callable $get, callable $set) {
								static::calculateFields($get, $set);
							})
							->schema([
								Hidden::make('sub_total'),
								Hidden::make('rates_details'),
								Hidden::make('details_date'),
								Hidden::make('company_id')
									->default(fn($get) => $get('../../company_id')),
								Hidden::make('client_id')
									->default(fn($get) => $get('../../client_id')),
								RichEditor::make('description')
									->extraInputAttributes(['style' => 'min-height: 10rem; max-height: 10vh; overflow-y: auto;'])
									->toolbarButtons([]),

								TextInput::make('quantity')->numeric()->required()->live(onBlur: true)
									->extraInputAttributes(['class' => 'text-end'])
									->afterStateUpdated(function (callable $set, $state, callable $get) {
										$set('sub_total', $state * $get('price'));
										static::calculateFields($get, $set);
									}),
								TextInput::make('price')->numeric()->required()->live(onBlur: true)
									->prefix(fn($get) => $get('../../currency_symbol'))
									->extraInputAttributes(['class' => 'text-end'])
									->afterStateUpdated(function (callable $set, $state, callable $get) {
										$set('sub_total', $state * $get('quantity'));
										static::calculateFields($get, $set);
									}),
								Placeholder::make('subTotal')
									->hiddenLabel()
									->extraAttributes(['class' => 'text-end font-bold'])
									->content(fn($get) => $get('../../currency_symbol') . ' ' . number_format($get('sub_total'), 2, ',', '.'))
							])->columnSpan(12)
							->columnStart(0)
							->afterStateHydrated(function (callable $set, callable $get) {
								static::calculateFields($get, $set);
							}),

						Placeholder::make('line')
							->hiddenLabel()->columnSpanFull()
							->content(new HtmlString('<hr>')),


						Group::make([
							Hidden::make('invoice_amount'),
							Hidden::make('inv_sub_total'),

							Placeholder::make('Sub Total')
								->inlineLabel()
								->extraAttributes(['class' => 'text-end font-bold'])
								->content(fn($get) => $get('currency_symbol') . ' ' . number_format($get('inv_sub_total'), 2, ',', '.')),

							TextInput::make('booking_fee')
								->inlineLabel()
								->numeric()
								->prefix(fn($get) => $get('currency_symbol'))
								->extraInputAttributes(['class' => 'text-end'])
								->live(onBlur: true)
								->hidden(fn($get) => $get('parent_invoice_id') === null)
								->afterStateUpdated(function (callable $set, callable $get) {
									static::calculateFields($get, $set);
								})
								->label('Booking Fee'),

							TextInput::make('rates')
								->inlineLabel()
								->numeric()
								->prefix('Rp')
								->hidden(fn($get) => $get('parent_invoice_id') === null)
								->extraInputAttributes(['class' => 'text-end'])
								->required()
								->live(onBlur: true)
								->afterStateUpdated(function (callable $set, callable $get) {
									static::calculateFields($get, $set);
								})
								->label('Rates'),
							Placeholder::make('Total')
								->inlineLabel()
								->reactive()
								->extraAttributes(['class' => 'text-end font-bold'])
								->content(function ($get) {
									$currencySymbol = $get('currency_symbol');
									$invTotal = number_format($get('invoice_amount'), 2, ',', '.');

									$companyId = $get('company_id');

									$companyType = $companyId ? Company::where('id', $companyId)->value('type') : null;

									$symbol = ((int) $companyType === 1) ? $currencySymbol : 'Rp';

									return new HtmlString("<div>{$symbol} {$invTotal}</div>");
								}),
						])->columnSpan(6)->columnStart(7),

						TextInput::make('amount_inword')
							->label('In Words')
							->columnSpanFull(),

						RichEditor::make('remarks')
							->label('Remarks')
							->extraInputAttributes(['style' => 'min-height: 5rem; max-height: 5vh; overflow-y: auto;'])
							->toolbarButtons([])
							->columnSpanFull(),
					]),

				Section::make('Bank Information')
					->schema([
						Select::make('bankRepeaterSelect')
							->label('Select Banks to Include')
							->multiple()
							->reactive()
							->placeholder('Select bank(s) to include.')
							->helperText('Choose which banks to include in this invoice template. This setting applies to all invoices from this company.')
							->options(fn ($get) => CompanyBank::where('company_id', $get('company_id'))->pluck('bank_name','id'))
							->afterStateUpdated(function ($state, $get, $set) {
								$companyId = $get('company_id');
								if (!$companyId) return;
								$resetCount = CompanyBank::where('company_id', $companyId)
									->update(['include_in_invoice' => false]);
								if ($state && is_array($state) && count($state) > 0) {
									$updateCount = CompanyBank::whereIn('id', $state)
										->update(['include_in_invoice' => true]);
								}
								$companyBank = CompanyBank::where('company_id', $companyId)->where('include_in_invoice', true)->get()->toArray();
								$set('bankRepeaterTrigger', time());
								$set('bankRepeater', $companyBank);
							}),

						Hidden::make('bankRepeaterTrigger'),

						TableRepeater::make('bankRepeater')
							// ->relationship('companyBanks')
							->reactive()
							->addable(false)
							->hiddenLabel()
							->defaultItems(0) // Start with no items
							->deletable(false) // Disable default delete to prevent actual record deletion
							->live() // Make it live reactive
							->afterStateHydrated(function ($get, $set) {
								$companyId = $get('company_id');
								$companyBank = CompanyBank::where('company_id', $companyId)->where('include_in_invoice', true)->get()->toArray();
								$set('bankRepeater', $companyBank);
							})
							->headers([
								Header::make('Bank Name')->width('35%'),
								Header::make('Account Name')->width('35%'),
								Header::make('Account No.')->width('25%'),
								// Header::make('Actions')->width('5%'),
							])
							->schema([
								Placeholder::make('bankName')->hiddenLabel()->content(fn ($get) => $get('bank_name')),
								Placeholder::make('bankName')->hiddenLabel()->content(fn ($get) => $get('bank_acc_name')),
								Placeholder::make('bankName')->hiddenLabel()->content(fn ($get) => $get('bank_acc_no')),
								Hidden::make('bank_name'),
								Hidden::make('bank_acc_name'),
								Hidden::make('bank_acc_no'),
								Hidden::make('id'),
								Hidden::make('company_id'),
								Hidden::make('bank_code'),
								Hidden::make('bank_acc_address'),
								Hidden::make('bank_address'),
								Hidden::make('bank_correspondent'),
								Hidden::make('swift'),
								Hidden::make('swift_correspondent'),
								Hidden::make('routing_no'),
								Hidden::make('transit'),
								Hidden::make('tt_charge'),
								Hidden::make('iban'),
								Hidden::make('institution'),
								Hidden::make('bsb'),
								Hidden::make('branch_code'),
								Hidden::make('sort_code'),
								Hidden::make('branch_bank'),
								Hidden::make('ABA'),
								Hidden::make('IFSC'),
								Hidden::make('custom_columns'),
								Hidden::make('custom_columns_data'),
							])
							->extraItemActions([
								Action::make('repeaterBankDetails')
									->icon('heroicon-o-pencil')
									->label('Edit Bank Info')
									->modalHeading(
										fn($component, array $arguments) =>
										'Edit Bank: ' . ($component->getItemState($arguments['item'])['bank_acc_name'] ?? 'New Bank')
									)
									->modalDescription(
										fn($component, array $arguments) =>
										'Bank: ' . ($component->getItemState($arguments['item'])['bank_name'] ?? '-')
									)
									->fillForm(function ($component, array $arguments) {
										$itemState = $component->getItemState($arguments['item']);

										// Convert custom_columns JSON to custom_columns_data array for modal form
										if (isset($itemState['custom_columns']) && is_string($itemState['custom_columns'])) {
											$customColumns = json_decode($itemState['custom_columns'], true);
											if (is_array($customColumns)) {
												$customColumnsData = [];
												foreach ($customColumns as $key => $value) {
													$customColumnsData[] = [
														'key' => $key,
														'type' => $value['type'] ?? 'text',
														'value' => $value['value'] ?? '',
													];
												}
												$itemState['custom_columns_data'] = $customColumnsData;
											}
										} elseif (isset($itemState['custom_columns']) && is_array($itemState['custom_columns'])) {
											// If already array, convert to custom_columns_data format
											$customColumnsData = [];
											foreach ($itemState['custom_columns'] as $key => $value) {
												$customColumnsData[] = [
													'key' => $key,
													'type' => $value['type'] ?? 'text',
													'value' => $value['value'] ?? '',
												];
											}
											$itemState['custom_columns_data'] = $customColumnsData;
										}

										return $itemState;
									})
									->form(static::getBankDetailsModalForm())
									->action(function (array $data, $component, array $arguments) {
										$processedData = static::processBankRepeaterCustomColumns($data);

										// Simpan ke database langsung jika ada id
										if (isset($processedData['id'])) {
											CompanyBank::where('id', $processedData['id'])->update($processedData);
										}

										// Tetap update state agar perubahan langsung terlihat di repeater
										$currentState = $component->getState();
										$currentState[$arguments['item']] = array_merge(
											$currentState[$arguments['item']] ?? [],
											$processedData
										);
										$component->state($currentState);
									}),
							])
							->addActionLabel('Add Bank Account')
							->reorderable(false)
							->collapsible()
							->itemLabel(
								fn(array $state): ?string => ($state['bank_acc_name'] ?? 'New Bank') . ' - ' . ($state['bank_name'] ?? '')
							),
					]),
			]);
	}

	public static function table(Table $table): Table
	{
		return $table
			->columns([
				Tables\Columns\TextColumn::make('invoice_no')
					->label('Invoice No')
					->searchable()
					->sortable(),
				Tables\Columns\TextColumn::make('invoice_date')
					->label('Invoice Date')
					->date()
					->sortable(),
				Tables\Columns\TextColumn::make('due_date')
					->label('Due Date')
					->date()
					->sortable(),
				Tables\Columns\TextColumn::make('company.name')
					->label('Company')
					->searchable()
					->sortable(),
				Tables\Columns\TextColumn::make('client.name')
					->label('Client')
					->searchable()
					->sortable(),
				Tables\Columns\TextColumn::make('invoice_amount')
					->label('Amount')
					->money(fn($record) => $record->currency->symbol ?? 'Rp')
					->sortable(),
				Tables\Columns\TextColumn::make('chain_position')
					->label('Chain Position')
					->badge()
					->getStateUsing(function ($record) {
						if (!$record->parent_invoice_id) {
							return 'Main Invoice';
						}

						// Count chain depth
						$depth = 1;
						$current = $record;
						while ($current->parent_invoice_id) {
							$depth++;
							$current = $current->parentInvoice;
						}
						return "Chain Level {$depth}";
					})
					->color(fn($record) => $record->parent_invoice_id ? 'info' : 'primary')
					->sortable(false),

				Tables\Columns\TextColumn::make('status')
					->badge()
					->color(fn(string $state): string => match ($state) {
						'Draft' => 'gray',
						'Issued' => 'warning',
						'Closed' => 'success',
						default => 'gray',
					})
					->searchable()
					->sortable(),
				Tables\Columns\TextColumn::make('created_at')
					->label('Created At')
					->dateTime()
					->sortable()
					->toggleable(isToggledHiddenByDefault: true),
				Tables\Columns\TextColumn::make('updated_at')
					->label('Updated At')
					->dateTime()
					->sortable()
					->toggleable(isToggledHiddenByDefault: true),
			])
			->filters([
				Tables\Filters\SelectFilter::make('status')
					->options([
						'Draft' => 'Draft',
						'Issued' => 'Issued',
						'Closed' => 'Closed',
					]),
				Tables\Filters\Filter::make('invoice_date')
					->form([
						Forms\Components\DatePicker::make('from'),
						Forms\Components\DatePicker::make('until'),
					])
					->query(function (Builder $query, array $data): Builder {
						return $query
							->when(
								$data['from'],
								fn(Builder $query, $date): Builder => $query->whereDate('invoice_date', '>=', $date),
							)
							->when(
								$data['until'],
								fn(Builder $query, $date): Builder => $query->whereDate('invoice_date', '<=', $date),
							);
					}),
			])
			->actions([
				Tables\Actions\ViewAction::make(),
				Tables\Actions\EditAction::make(),
			])
			->bulkActions([
				Tables\Actions\BulkActionGroup::make([
					Tables\Actions\DeleteBulkAction::make(),
				]),
			]);
	}

	public static function getRelations(): array
	{
		return [
			//
		];
	}

	public static function getPages(): array
	{
		return [
			'index' => Pages\ListInvoices::route('/'),
			'create' => Pages\CreateInvoice::route('/create'),
			'view' => Pages\ViewInvoice::route('/{record}'),
			'edit' => Pages\EditInvoice::route('/{record}/edit'),
		];
	}

	protected static function updateInvoiceNumberPlaceholder(callable $set, $state): void
	{
		if (!$state) {
			$set('invoice_no_placeholder', 'Invoice No');
			return;
		}

		$lastInvoice = Invoice::where('company_id', $state)
			->orderBy('created_at', 'desc')
			->first();

		$placeholder = $lastInvoice ? 'Last: ' . $lastInvoice->invoice_no : 'Invoice No';
		$set('invoice_no_placeholder', $placeholder);
	}

	/**
	 * Update form with bank account data
	 *
	 * @param callable $get Getter function for form state
	 * @param callable $set Setter function for form state
	 * @param mixed $state Current state value (bank account ID)
	 * @return void
	 */
	// OLD METHOD - REMOVED: getBankAccountData()
	// This method was for old single bank system, replaced by bankRepeater system


	/**
	 * Update invoice details based on selected business type
	 *
	 * @param callable $get Getter function for form state
	 * @param callable $set Setter function for form state
	 * @param mixed $state Current state value (business type ID)
	 * @return void
	 */
	public static function businessTypeSelect(callable $set, $state): void
	{
		// Clear existing invoice details
		$set('invDetails', []);

		// If a business type is selected, populate with template items
		if (!is_null($state)) {
			// Get all master invoice details for this business type in a single query
			$masterInvoiceDetails = MasterInvoiceDetail::where('business_type', $state)
				->get();

			// Map the details to the format needed for the form
			if ($masterInvoiceDetails->isNotEmpty()) {
				$invDetails = $masterInvoiceDetails->map(function ($template) {
					return [
						'description' => $template->description,
						'quantity' => $template->quantity,
						'price' => $template->price,
						'sub_total' => $template->quantity * $template->price,
						// company_id dan client_id akan diset oleh calculateFields atau default values
					];
				})->toArray();

				$set('invDetails', $invDetails);
			}
		}
	}


	/**
	 * Calculate and update invoice totals and related fields
	 *
	 * @param callable $get Getter function for form state
	 * @param callable $set Setter function for form state
	 * @return void
	 */
	public static function calculateFields($get, $set): void
	{
		$items = $get('invDetails') ?? [];
		$rates = $get('rates');
		$date = $get('invoice_date');

		// If items is not an array, exit early
		if (!is_array($items)) {
			return;
		}

		$totalRows = 0;

		// Calculate total from all invoice detail items
		foreach ($items as $key => $item) {
			$quantity = $item['quantity'] ?? 0;
			$price = $item['price'] ?? 0;
			$totalRow = $price * $quantity;

			// Update metadata for each row
			$set("invDetails.$key.rates_details", $rates);
			$set("invDetails.$key.details_date", $date);
			$set("invDetails.$key.company_id", $get('company_id'));
			$set("invDetails.$key.client_id", $get('client_id'));

			$totalRows += $totalRow;
		}

		$parentId = $get('parent_invoice_id');
		if ($parentId === null || $parentId === '') {
			$locale = 'us';
			$rates = 1;
			$currencyId = $get('currency_id');

			// Only fetch currency if we have a valid ID
			if ($currencyId) {
				$currency = Currency::find($currencyId);
				$wordSuffix = $currency ? ($currency->suffix ?? '') : '';
			} else {
				$wordSuffix = '';
			}
		} else {
			$locale = 'id';
			$rates = $get('rates');
			$wordSuffix = 'Rupiah';
		}

		// Calculate final amounts
		$bookingFee = $get('booking_fee') ?? 0;
		$invSubTotal = $totalRows;
		$invoiceAmount = $invSubTotal + $bookingFee;

		$totalRates = $invoiceAmount * $rates;

		// Format spell total without hyphens and ensure US standard format
		$spellTotal = Number::spell($totalRates, locale: $locale);

		// Remove hyphens and ensure proper formatting for US standard
		$cleanSpellTotal = str_replace('-', ' ', $spellTotal);

		// Capitalize first letter of each word and add suffix
		$formattedSpell = ucwords($cleanSpellTotal);

		// Add "Only" at the end for US format, or just suffix for Indonesian
		if ($locale === 'us') {
			$formattedSpell = $formattedSpell . ' ' . $wordSuffix;
		} else {
			$formattedSpell = $formattedSpell . ' ' . $wordSuffix;
		}

		// Update form state with calculated values
		$set('inv_sub_total', $invSubTotal);
		$set('invoice_amount', $totalRates);
		$set('amount_inword', $formattedSpell);
	}


	/**
	 * Update form fields based on selected company template
	 *
	 * @param callable $get Getter function for form state
	 * @param callable $set Setter function for form state
	 * @param mixed $state Current state value (company ID)
	 * @return void
	 */
	public static function templateSelect(callable $set, $state): void
	{
		// Reset all fields if no company is selected
		if (is_null($state)) {
			// Clear invoice details
			$set('invDetails', []);

			// Reset basic invoice fields
			$set('invoice_no', null);
			$set('currency_id', null);
			$set('invoice_date', today());
			$set('invoice_due', now()->addDays(14));
			$set('customer_id', null);
			$set('amount_inword', null);
			$set('remarks', null);

			// Clear bank repeater
			$set('bankRepeater', []);
		} else {
			// Get company and its default bank account
			$record = Company::find($state);

			if (!$record) {
				return;
			}

			// Get business type from company and populate invoice details
			$businessType = $record->business_type;

			if ($businessType) {
				// Clear existing invoice details
				$set('invDetails', []);

				// Get master invoice details for this business type
				$masterInvoiceDetails = MasterInvoiceDetail::where('business_type', $businessType)
					->get();

				if ($masterInvoiceDetails->isNotEmpty()) {
					// Map the details to the format needed for the form
					$invDetails = $masterInvoiceDetails->map(function ($template) {
						return [
							'description' => $template->description,
							'quantity' => $template->quantity,
							'price' => $template->price,
							'sub_total' => $template->quantity * $template->price,
							// company_id dan client_id akan diset oleh calculateFields atau default values
						];
					})->toArray();

					$set('invDetails', $invDetails);
				}
			}

			// Auto-populate default bank account if available
			$defaultBank = CompanyBank::where('company_id', $state)
				->orderByDesc('is_default')
				->first();

			if ($defaultBank) {
				$bankData = [
					'bank_name' => $defaultBank->bank_name,
					'bank_acc_name' => $defaultBank->bank_acc_name,
					'bank_code' => $defaultBank->bank_code,
					'bank_acc_no' => $defaultBank->bank_acc_no,
					'bank_acc_address' => $defaultBank->bank_acc_address,
					'bank_address' => $defaultBank->bank_address,
					'bank_correspondent' => $defaultBank->bank_correspondent,
					'swift' => $defaultBank->swift,
					'swift_correspondent' => $defaultBank->swift_correspondent,
					'routing_no' => $defaultBank->routing_no,
					'transit' => $defaultBank->transit,
					'tt_charge' => $defaultBank->tt_charge,
					'iban' => $defaultBank->iban,
					'institution' => $defaultBank->institution,
					'bsb' => $defaultBank->bsb,
					'branch_code' => $defaultBank->branch_code,
					'sort_code' => $defaultBank->sort_code,
					'branch_bank' => $defaultBank->branch_bank,
					'ABA' => $defaultBank->ABA,
					'IFSC' => $defaultBank->IFSC,
					'custom_columns_data' => static::convertCustomColumnsToRepeaterFormat($defaultBank->custom_columns ?? []),
				];

				$set('bankRepeater', [$bankData]);
			}
		}
	}

	/**
	 * Convert custom columns from JSON format to repeater format
	 *
	 * @param array $customColumns
	 * @return array
	 */
	public static function convertCustomColumnsToRepeaterFormat(array $customColumns): array
	{
		$customColumnsData = [];

		foreach ($customColumns as $key => $value) {
			$customColumnsData[] = [
				'key' => $key,
				'type' => $value['type'] ?? 'text',
				'value' => $value['value'] ?? '',
			];
		}

		return $customColumnsData;
	}

	/**
	 * Get bank details modal form schema
	 *
	 * @return array
	 */
	public static function getBankDetailsModalForm(): array
	{
		return [
			Group::make([
				Fieldset::make('Bank Information')
					->schema([
						Hidden::make('id'),
						Hidden::make('company_id'),
						TextInput::make('bank_name')->label('Bank Name')->required()->columnSpanFull(),
						TextInput::make('bank_acc_name')->label('Account Name')->required()->columnSpanFull(),
						TextInput::make('bank_code')->label('Bank Code')->columnSpanFull(),
						TextInput::make('bank_acc_no')->label('Account No')->columnSpanFull(),
						RichEditor::make('bank_acc_address')
							->label('Account Address')
							->extraInputAttributes(['style' => 'min-height: 5rem; max-height: 5vh; overflow-y: auto;'])
							->toolbarButtons([])
							->columnSpanFull(),
						RichEditor::make('bank_address')
							->label('Bank Address')
							->extraInputAttributes(['style' => 'min-height: 5rem; max-height: 5vh; overflow-y: auto;'])
							->toolbarButtons([])
							->columnSpanFull(),
						TextInput::make('bank_correspondent')->label('Bank Correspondent')->columnSpanFull(),
					])->columnSpan(1),

				Fieldset::make('Additional Information')
					->schema([
						RichEditor::make('swift')
							->label('Swift Code')
							->extraInputAttributes(['style' => 'min-height: 5rem; max-height: 5vh; overflow-y: auto;'])
							->toolbarButtons([])
							->columnSpanFull(),
						TextInput::make('swift_correspondent')->label('Swift Correspondent')->columnSpanFull(),
						TextInput::make('routing_no')->label('Routing No')->columnSpanFull(),
						TextInput::make('transit')->label('Transit Code')->columnSpanFull(),
						TextInput::make('tt_charge')->label('TT Charge')->columnSpanFull(),
						TextInput::make('institution')->label('Institution Code')->columnSpanFull(),
						TextInput::make('iban')->label('IBAN')->columnSpanFull(),
						TextInput::make('bsb')->label('BSB')->columnSpanFull(),
						TextInput::make('branch_code')->label('Branch Code')->columnSpanFull(),
						TextInput::make('sort_code')->label('Sort Code')->columnSpanFull(),
						TextInput::make('branch_bank')->label('Branch Bank')->columnSpanFull(),
						TextInput::make('ABA')->label('ABA')->columnSpanFull(),
						TextInput::make('IFSC')->label('IFSC')->columnSpanFull(),
					])->columnSpan(1),
			])->columns(2),

			Fieldset::make('Custom Bank Fields')
				->schema([
					Placeholder::make('custom_bank_info')
						->hiddenLabel()
						->columnSpanFull()
						->content('Add custom fields for additional bank information that is not covered by standard fields.'),

					Repeater::make('custom_columns_data')
						->hiddenLabel()
						->columnSpanFull()
						->schema([
							TextInput::make('key')
								->label('Field Name')
								->required()
								->inlineLabel()
								->maxLength(20)
								->live(onBlur: true)
								->afterStateUpdated(function (callable $set, callable $get, $state) {
									if (!$state) {
										$set('duplicate_warning', null);
										return;
									}

									// Get delivered bank columns (from InvoiceBank model)
									$deliveredBankColumns = [
										'bank_acc_name',
										'bank_code',
										'bank_acc_no',
										'bank_acc_address',
										'bank_name',
										'bank_address',
										'bank_correspondent',
										'swift',
										'swift_correspondent',
										'routing_no',
										'transit',
										'tt_charge',
										'iban',
										'institution',
										'bsb',
										'branch_code',
										'sort_code',
										'branch_bank',
										'ABA',
										'IFSC'
									];

									// Get existing custom keys from current form data
									$customColumnsData = $get('../../custom_columns_data') ?? [];

									// Count occurrences of current key
									$keyCount = 0;
									foreach ($customColumnsData as $item) {
										if (!empty($item['key']) && $item['key'] === $state) {
											$keyCount++;
										}
									}

									// Check for duplicates
									$isDuplicateDelivered = in_array($state, $deliveredBankColumns);
									$isDuplicateCustom = $keyCount > 1;

									if ($isDuplicateDelivered) {
										// Don't clear value, just show warning
										$set('duplicate_warning', 'Field name conflicts with bank field');
									} elseif ($isDuplicateCustom) {
										// Don't clear value, just show warning
										$set('duplicate_warning', 'Field name already used in custom fields');
									} else {
										$set('duplicate_warning', null);
									}
								})
								->helperText(function ($get) {
									$warning = $get('duplicate_warning');
									return $warning ? $warning : 'Maximum 20 characters. Must be unique.';
								})
								->suffixIcon(fn($get) => $get('duplicate_warning') ? 'heroicon-o-exclamation-triangle' : null)
								->suffixIconColor(fn($get) => $get('duplicate_warning') ? 'danger' : null)
								->extraAttributes(function ($get) {
									return $get('duplicate_warning') ? ['class' => 'border-red-300'] : [];
								}),

							Select::make('type')
								->label('Field Type')
								->inlineLabel()
								->options([
									'text' => 'Text',
									'textarea' => 'Text Area',
									'richtext' => 'Rich Text',
								])
								->default('text')
								->required()
								->live(),

							TextInput::make('value')
								->label('Value')
								->inlineLabel()
								->visible(fn($get) => $get('type') === 'text'),

							Textarea::make('value')
								->label('Value')
								->inlineLabel()
								->rows(3)
								->visible(fn($get) => $get('type') === 'textarea'),

							RichEditor::make('value')
								->label('Value')
								->toolbarButtons(['bold', 'italic', 'bulletList', 'orderedList'])
								->visible(fn($get) => $get('type') === 'richtext'),

							TextInput::make('duplicate_warning')
								->hidden(),
						])
						->addActionLabel('Add Custom Bank Field')
						->reorderable(false)
						->collapsible()
						->itemLabel(fn(array $state): ?string => $state['key'] ?? 'New Field')
						->defaultItems(0),
				]),
		];
	}

	/**
	 * Process bank repeater custom columns data before saving
	 *
	 * @param array $data
	 * @return array
	 */
	public static function processBankRepeaterCustomColumns(array $data): array
	{
		if (isset($data['custom_columns_data']) && is_array($data['custom_columns_data'])) {
			$customColumns = [];
			$deliveredBankColumns = [
				'bank_acc_name',
				'bank_code',
				'bank_acc_no',
				'bank_acc_address',
				'bank_name',
				'bank_address',
				'bank_correspondent',
				'swift',
				'swift_correspondent',
				'routing_no',
				'transit',
				'tt_charge',
				'iban',
				'institution',
				'bsb',
				'branch_code',
				'sort_code',
				'branch_bank',
				'ABA',
				'IFSC'
			];

			foreach ($data['custom_columns_data'] as $item) {
				// Only require key to be present and not empty
				if (!empty($item['key'])) {
					// Skip if key is in delivered columns
					if (in_array($item['key'], $deliveredBankColumns)) {
						continue;
					}

					// Skip if key already exists in current custom columns
					if (array_key_exists($item['key'], $customColumns)) {
						continue;
					}

					// Allow empty values - user might want to save field structure
					$customColumns[$item['key']] = [
						'type' => $item['type'] ?? 'text',
						'value' => $item['value'] ?? ''
					];
				}
			}

			$data['custom_columns'] = empty($customColumns) ? null : $customColumns;
		}

		// Remove the temporary field
		unset($data['custom_columns_data']);

		return $data;
	}
}
