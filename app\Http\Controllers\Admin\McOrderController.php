<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\McDeposit;
use App\Models\McOrder;
use Illuminate\Http\Request;

class McOrderController extends Controller
{
	public function printOrder($record)
	{
		$payload = $this->orderPayload($record);
		return view('mc.mc-order-print-view', compact('payload'));
	}

	public function orderPayload($record)
	{
		// Ambil data order dengan relasi menggunakan eager loading
		$order = McOrder::with([
			'customer',
			'currency',
			'bank',
		])->findOrFail($record);

		$customerId = $order->mc_customer_id;

		// Hitung total deposit yang valid
		$allDeposits = McDeposit::where('mc_customer_id', $customerId)
			->where('trx_type', 'incoming')
			->sum('amount');

		$allOrders = McDeposit::where('mc_customer_id', $customerId)
			->where('trx_type', 'order')
			->sum('amount');

		// Hitung total untuk order saat ini
		$orderTotal = ($order->amount ?? 0) * ($order->sell_rates ?? 1);
		if (str_contains($order->order_code, 'CLS')) {
			$orderCurrent = $order->total_order;
		} else {
			$orderCurrent = $orderTotal + ($order->charges ?? 0);
		}

		// Hitung deposit saat ini dan saldo
		$currentDeposits = ($allDeposits - $allOrders) + $orderCurrent;
		$balance = $allDeposits - $allOrders;
		// Buat payload
		return [
			'id'			=> $order->id,
			'orderCode'		=> $order->order_code ?? '',
			'customer'		=> $order->customer->name ?? '',
			'customerId'	=> $order->customer->customer_code ?? '',
			'fxSymbol'		=> $order->currency->symbol ?? '',
			'fxName'		=> $order->currency->name ?? '',
			'amount'		=> $order->amount ?? 0,
			'rate'			=> $order->sell_rates ?? 1,
			'charges'		=> $order->charges ?? 0,
			'totalCurrent'	=> $orderCurrent,
			'totalOrder'	=> $allOrders,
			'deposit'		=> $currentDeposits,
			'balance'		=> $balance,
			'orderDate'		=> $order->created_at,
			'bankName'		=> $order->bank->name ?? '',
			'bankAccNo'		=> $order->bank->account_no ?? '',
			'bankAccName'	=> $order->bank->account_name ?? '',
			'fontFamily'    => 'Inter',
			'fontSource'    => 'https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap',
			'paperSize'     => 'Legal',
			'marginTop'     => 5,
			'marginRight'   => 10,
			'marginBottom'  => 10,
			'marginLeft'    => 10,
		];
	}
}
