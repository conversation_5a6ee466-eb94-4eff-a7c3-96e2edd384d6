<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\CompanyDepositoResource\Pages;
use App\Filament\Admin\Resources\CompanyDepositoResource\RelationManagers;
use App\Models\Company;
use App\Models\CompanyBank;
use App\Models\CompanyDeposito;
use App\Models\Order;
use Filament\Forms;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\{ViewAction, DeleteAction, EditAction, Action, BulkActionGroup, DeleteBulkAction};
use Filament\Tables\Columns\{TextColumn};
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Number;

class CompanyDepositoResource extends Resource
{
    protected static ?string $model = CompanyDeposito::class;
	public static function getNavigationLabel(): string
	{
		return 'Deposito';
	}
    protected static ?string $navigationIcon = 'icon-piggy-bank';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
				Group::make()
					->schema([
						Select::make('company_id')
							->label('Select Company')
							->options(fn() => Company::where('type', 2)->pluck('name', 'id'))
							->searchable()
							->reactive()
							->required(),
						Select::make('trx_type')
							->label('Transaction Type')
							->reactive()
							->afterStateUpdated(function (callable $set, $state) {
								if($state === 'order') {
									$set('order_id', null);
									$set('amount', null);
									$set('trx_date', null);
									$set('description', null);
									$set('bank_id', null);
								} else {
									$set('order_id', null);
									$set('amount', null);
									$set('trx_date', null);
									$set('description', null);
									$set('bank_id', null);
								}
							})
							->options([
								'in' => 'Incoming',
								'out' => 'Outgoing',
								'order' => 'Order (Outgoing)',
							])
							->required(),
						Textarea::make('description')
							->reactive()
							->maxLength(191),
					]),
					Group::make()
						->schema([
							Select::make('order_id')
								->label('Select Order')
								->reactive()
								->options(fn($get) =>
									Order::where('company_id', $get('company_id'))
										->whereNotIn('id', DB::table('company_depositos')->whereNotNull('order_id')->pluck('order_id'))
										->pluck('order_no', 'id')
								)
								->visible(fn($get) => $get('trx_type') === 'order')
								->searchable()
								->afterStateUpdated(function (callable $set, $state) {
									$order = Order::find($state);
									$set('amount', $order->total * $order->rates);
									$set('trx_date', $order->order_date);
									$set('description', 'Payment for order ' . $order->order_no);
									$set('bank_id', $order->bank_id);
								})
								->reactive(),
							DatePicker::make('trx_date')->reactive()->default(now()),
							Select::make('bank_id')
								->label('Select Bank')
								->options(fn($get) => CompanyBank::where('company_id', $get('company_id'))->pluck('bank_name', 'id'))
								->searchable()
								->reactive()
								->reactive(),
							TextInput::make('amount')
								->live(onBlur:true)
								->numeric()
								->afterStateUpdated(function ($set, $state) {
									$spellTotal = Number::spell($state, locale: 'id');
									Log::info($spellTotal);
									$set('amountInWord', $spellTotal);
								})
								->helperText(fn ($get, $state) => number_format($state ?? 0,2, ',', '.'). ' | ' . $get('amountInWord')),
							Placeholder::make('amountInWordPlaceholder')
								->hiddenLabel()
								->reactive()
								->content(fn ($get) => $get('amountInWord'))

						])
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('company.name')
                    ->numeric()
					->limit(16)
                    ->sortable()
					->tooltip(function (TextColumn $column): ?string {
						$state = $column->getState();

						if (strlen($state) <= $column->getCharacterLimit()) {
							return null;
						}
						return $state;
					}),
                TextColumn::make('bank.bank_name')
                    ->numeric()
					->limit(20)
                    ->sortable()
					->tooltip(function (TextColumn $column): ?string {
						$state = $column->getState();

						if (strlen($state) <= $column->getCharacterLimit()) {
							return null;
						}
						return $state;
					}),
                TextColumn::make('trx_type')
					->badge()
					->color(fn ($state) => match ($state) {
						'in' => 'success',
						'out' => 'danger',
						'order' => 'warning',
					})
					->formatStateUsing(fn ($state) => match ($state) {
						'in' => 'Incoming',
						'out' => 'Outgoing',
						'order' => 'Order (Outgoing)',
					}),
                TextColumn::make('order.order_no')
                    ->numeric()
                    ->sortable(),
                TextColumn::make('trx_date')
                    ->date()
                    ->sortable(),
                TextColumn::make('amount')
                    ->numeric()
                    ->sortable()
					->money('IDR'),
            ])
            ->filters([
                Filter::make('trx_date')
					->form([
						DatePicker::make('from')->default(today()),
						DatePicker::make('until')->default(today()),
					])
					->query(function (Builder $query, array $data): Builder {
						return $query
							->when(
								$data['from'],
								fn(Builder $query, $date): Builder => $query->whereDate('trx_date', '>=', $date),
							)
							->when(
								$data['until'],
								fn(Builder $query, $date): Builder => $query->whereDate('trx_date', '<=', $date),
							);
					}),
				SelectFilter::make('trx_type')
                    ->options([
                        'in' => 'Incoming',
                        'out' => 'Outgoing',
                        'order' => 'Order (Outgoing)',
                    ]),
				SelectFilter::make('company_id')
                    ->label('Company')
					->searchable()
					->options(Company::where('type', 2)->where('name', 'like', 'PT%')->pluck('name', 'id')),
            ])
            ->actions([
                ViewAction::make()
					->iconButton(),
                EditAction::make()->iconButton(),
                DeleteAction::make()
                    ->iconButton()
                    ->visible(fn($record) => is_null($record->order_id))
                    ->tooltip(fn($record) => $record->order_id ? 'Deposito terkait Order tidak dapat dihapus manual' : 'Hapus deposito'),
                Action::make('audit')
                    ->label('Audit Trail')
                    ->icon('heroicon-o-eye')
                    ->iconButton()
                    ->tooltip('View Audit Trail')
                    ->modalHeading('Audit Trail')
                    ->modalContent(function ($record) {
                        $auditTrail = CompanyDeposito::withTrashed()
                            ->where('order_id', $record->order_id)
                            ->orderBy('created_at')
                            ->get();

                        $html = '<div class="space-y-2">';
                        foreach ($auditTrail as $item) {
                            $status = $item->deleted_at ? 'Deleted' : 'Active';
                            $statusColor = $item->deleted_at ? 'text-red-600' : 'text-green-600';
                            $html .= "<div class='p-2 border rounded'>";
                            $html .= "<div class='font-semibold'>Amount: " . number_format($item->amount, 2) . "</div>";
                            $html .= "<div>Date: {$item->trx_date}</div>";
                            $html .= "<div>Created: {$item->created_at}</div>";
                            $html .= "<div class='{$statusColor}'>Status: {$status}</div>";
                            $html .= "</div>";
                        }
                        $html .= '</div>';

                        return new \Illuminate\Support\HtmlString($html);
                    })
                    ->visible(fn($record) => $record->order_id !== null),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make()
                        ->action(function ($records) {
                            // Only delete records that are not related to orders
                            $deletableRecords = $records->filter(fn($record) => is_null($record->order_id));
                            $deletableRecords->each->delete();

                            $skippedCount = $records->count() - $deletableRecords->count();
                            if ($skippedCount > 0) {
                                \Filament\Notifications\Notification::make()
                                    ->title('Peringatan')
                                    ->body("{$skippedCount} deposito terkait Order dilewati karena tidak dapat dihapus manual.")
                                    ->warning()
                                    ->send();
                            }
                        }),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCompanyDepositos::route('/'),
            // 'create' => Pages\CreateCompanyDeposito::route('/create'),
            // 'view' => Pages\ViewCompanyDeposito::route('/{record}'),
            // 'edit' => Pages\EditCompanyDeposito::route('/{record}/edit'),
        ];
    }
	public static function shouldRegisterNavigation(): bool
	{
		$user = Auth::user();

		return $user && $user->hasAnyRole(['admin', 'Super Admin']);
	}

}
