<?php

namespace App\Filament\Admin\Widgets;

use App\Models\Company;
use App\Models\CompanyDepositSummary;
use App\Models\Order;
use Carbon\Carbon;
use Filament\Forms\Components\Select;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Number;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class trendsWidget extends BaseWidget
{
	// protected static ?int $sort = 2;
	protected int | string | array $columnSpan = 3;

	protected static string $view = 'filament.admin.widgets.cash-flow-trends';

	public static function canView(): bool
	{
		return Auth::user()->hasAnyRole(['admin', 'Super Admin']);
	}
	public static function canAccess(): bool
	{
		return Auth::user()->hasAnyRole(['admin', 'Super Admin']);
	}
	public function getViewData(): array
	{
		$now = Carbon::now('Asia/Jakarta');

		// Total Balance
		$totalBalance = CompanyDepositSummary::sum('balance') ?? 0;

		// Cash Flow Total
		$cashFlowData = DB::table('company_depositos')
			->whereNull('deleted_at')
			->selectRaw('
            SUM(CASE WHEN trx_type = "in" THEN amount ELSE 0 END) as total_in,
            SUM(CASE WHEN trx_type = "out" THEN amount ELSE 0 END) as total_out,
            SUM(CASE WHEN trx_type = "order" THEN amount ELSE 0 END) as total_order
        ')
			->first();

		$cashFlowTotal = ($cashFlowData->total_in ?? 0) - ($cashFlowData->total_out ?? 0) - ($cashFlowData->total_order ?? 0);

		// Low Balance Banks
		$lowBalanceThreshold = 1000000;
		$lowBalanceBanks = CompanyDepositSummary::where('balance', '<', $lowBalanceThreshold)->count();

		return [
			'totalBalance' => number_format($totalBalance, 2, ',', '.'),
			'cashFlowTotal' => number_format($cashFlowTotal, 2, ',', '.'),
			'lowBalanceBanks' => $lowBalanceBanks,
		];
	}
}
