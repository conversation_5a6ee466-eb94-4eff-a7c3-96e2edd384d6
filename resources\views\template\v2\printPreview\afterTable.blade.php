@php
	// Extract configuration
	$invoiceTemplate = $payload['invoice']->invoicetemplate;
	$layoutConfig = $invoiceTemplate?->layout_config ?? [];
	$repeaterLayout = $layoutConfig['repeater_layout'] ?? [];
@endphp

@foreach ($repeaterLayout as $row)
	@if ($row['row_position'] === 'after_table')
		<div class="">
			<table class="w-full">
				<tr>
					<td class="align-top w-1/2 pe-2">
						@if (!empty($row['column_1_content']))
							@foreach ((array) $row['column_1_content'] as $content)
								@include('template.v2.printPreview.switchcase')
							@endforeach
						@endif
					</td>
					@if ($row['column_size'] == 2)
						<td class="align-top w-1/2">
							@if (!empty($row['column_2_content']))
								@foreach ((array) $row['column_2_content'] as $content)
									@include('template.v2.printPreview.switchcase')
								@endforeach
							@endif
						</td>
					@endif
				</tr>
			</table>
		</div>
	@endif
@endforeach
