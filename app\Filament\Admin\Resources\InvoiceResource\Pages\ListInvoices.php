<?php

namespace App\Filament\Admin\Resources\InvoiceResource\Pages;

use App\Filament\Admin\Resources\InvoiceResource;
use App\Models\Company;
use App\Models\CompanyBank;
use App\Models\Invoice;
use App\Models\InvoiceApprovalLog;
use App\Models\InvoiceDetail;
use App\Models\MasterInvoiceDetail;
use Carbon\Carbon;
use Filament\Actions;
use Filament\Forms\Components\{DatePicker, Select, Textarea};
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Filament\Tables\Actions\{Action, ActionGroup, BulkActionGroup, DeleteBulkAction, EditAction, ViewAction};
use Filament\Tables\Columns\{TextColumn};
use Filament\Tables\Enums\ActionsPosition;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\QueryBuilder;
use Filament\Tables\Filters\QueryBuilder\Constraints\DateConstraint;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Contracts\Pagination\Paginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ListInvoices extends ListRecords
{
    protected static string $resource = InvoiceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    public function getDefaultActiveTab(): string
    {
        return 'Draft';
    }

    // protected function paginateTableQuery(Builder $query): Paginator
    // {
    //     return $query->simplePaginate(($this->getTableRecordsPerPage() === 'all') ? $query->count() : $this->getTableRecordsPerPage());
    // }

    public function getTabs(): array
    {
        return [
            'Draft' => Tab::make('Draft/New')->modifyQueryUsing(function (Builder $query) {
				$query->where(function ($q) {
					$q->where('status', 'Draft')->orWhereNull('status');
				})->where('created_at', today())->orWhere('invoice_date', today());
			})->badge(
				Invoice::query()->where('created_at', today())
					->orWhere('invoice_date', today())
					->where(function ($q) {
						$q->where('status', 'Draft')->orWhereNull('status');
					})
					->count()
			),

            'Issued' => Tab::make('Issued')->modifyQueryUsing(function (Builder $query){
                $query->where('status', 'Issued');
            })->badge(
				Invoice::query()->where('created_at', today())
					->orWhere('invoice_date', today())
					->where('status', 'Issued')->count()),

            'Closed' => Tab::make('Closed')->modifyQueryUsing(function (Builder $query){
                $query->where('status', 'Closed');
            })->badge(Invoice::query()->where('created_at', today())
				->orWhere('invoice_date', today())
				->where('status', 'Closed')->count()),

            // 'NewVerify' => Tab::make('Need Verify')->modifyQueryUsing(function (Builder $query){
            //     $query->where('verified_by', 0);
            // })->badge(Invoice::query()->where('verified_by', 0)->count()),

            // 'Verified' => Tab::make('Verified')->modifyQueryUsing(function (Builder $query){
            //     $query->where('verified_by', Auth::user()->id);
            // })->badge(Invoice::query()->where('verified_by', Auth::user()->id)->count()),

            // 'NewSupervised' => Tab::make('Need Supervision')->modifyQueryUsing(function (Builder $query){
            //     $query->where('supervised_by', 0);
            // })->badge(Invoice::query()->where('supervised_by', 0)->count()),

            // 'Supervised' => Tab::make('Supervised')->modifyQueryUsing(function (Builder $query){
            //     $query->where('supervised_by', Auth::user()->id);
            // })->badge(Invoice::query()->where('supervised_by', Auth::user()->id)->count()),

            'all' => Tab::make('All')
				->badge(Invoice::query()->where('created_at', today())
					->orWhere('invoice_date', today())
					->count()),
        ];
    }
    public function table(Table $table): Table
    {
        return $table
			->recordClasses(fn (Model $record) => is_null($record->order_id) ? 'bg-amber-50 dark:bg-amber-600' : null)
            ->modifyQueryUsing(function (Builder $query) {
                $user = Auth::user();
                $activeTab = $this->activeTab;

                // Terapkan filter tab jika bukan tab 'all'
                if ($activeTab && $activeTab !== 'all') {
                    // Pastikan filter tab diterapkan sesuai dengan definisi di getTabs()
                    if ($activeTab === 'Draft') {
						$query->where(function ($q) {
							$q->where('status', 'Draft')
							->orWhereNull('status');
						});
					} elseif ($activeTab === 'Issued') {
                        $query->where('status', 'Issued');
                    } elseif ($activeTab === 'Closed') {
                        $query->where('status', 'Closed');
                    }
                }

                // Semua user dibatasi pada range tahun
                $query->whereBetween('created_at', [
                    Carbon::now()->subYear()->startOfYear(), // 1 Jan tahun lalu
                    Carbon::now()->endOfYear(),              // 31 Des tahun ini
                ]);

                // Jika Staff Invoice, filter invoice milik sendiri
                if ($user->hasAnyRole(['Staff Invoice'])) {
                    // Gunakan where dengan closure untuk mengelompokkan kondisi OR
                    $query->where(function($subQuery) use ($user) {
                        $subQuery->where('invoice_create_by', $user->id)
                                ->orWhereNull('invoice_create_by');
                    });
                }
            })
            ->poll('5s')
            ->columns([
                TextColumn::make('company.name')
                    ->limit(16)
					->sortable()
					->searchable()
					->tooltip(function (TextColumn $column): ?string {
						$state = $column->getState();

						if (strlen($state) <= $column->getCharacterLimit()) {
							return null;
						}
						return $state;
					}),
                TextColumn::make('client.name')
                    ->limit(16)
                    ->sortable()
					->searchable()
					->tooltip(function (TextColumn $column): ?string {
						$state = $column->getState();

						if (strlen($state) <= $column->getCharacterLimit()) {
							return null;
						}
						return $state;
					}),
                TextColumn::make('parentInvoice.invoice_no')
                    ->limit(8)
					->sortable()
					->searchable()
					->tooltip(function (TextColumn $column): ?string {
						$state = $column->getState();

						if (strlen($state) <= $column->getCharacterLimit()) {
							return null;
						}
						return $state;
					}),
                TextColumn::make('invoice_no')
                    ->limit(8)
					->searchable()
					->sortable()
					->tooltip(function (TextColumn $column): ?string {
						$state = $column->getState();

						if (strlen($state) <= $column->getCharacterLimit()) {
							return null;
						}
						return $state;
					}),
                TextColumn::make('invoice_date')
                    ->date()
                    ->sortable(),
                TextColumn::make('invoice_amount')
                    ->numeric()
                    ->sortable()
					->alignEnd()
					->prefix(fn ($record) => $record->currency->symbol),
					// ->money(fn ($record) => $record->currency->symbol),
                TextColumn::make('chain_info')
                    ->label('Chain Info')
                    ->getStateUsing(function ($record) {
                        if (!$record->parent_invoice_id) {
                            // Main invoice - show child if exists
                            $child = $record->childInvoice()->first();
                            return $child ? "→ Child: #{$child->id}" : "Main (No chain)";
                        } else {
                            // Child invoice - show parent
                            return "← Parent: #{$record->parent_invoice_id}";
                        }
                    })
                    ->color(fn ($record) => $record->parent_invoice_id ? 'info' : 'gray')
                    ->icon(fn ($record) => $record->parent_invoice_id ? 'heroicon-o-arrow-left' : 'heroicon-o-arrow-right')
                    ->url(function ($record) {
                        if ($record->parent_invoice_id) {
                            return route('filament.admin.resources.invoices.view', $record->parent_invoice_id);
                        } else {
                            $child = $record->childInvoice()->first();
                            return $child ? route('filament.admin.resources.invoices.view', $child->id) : null;
                        }
                    })
                    ->openUrlInNewTab()
                    ->sortable(false),

                TextColumn::make('status')
                    ->badge()
                    ->formatStateUsing(function ($state, $record) {
                        $baseStatus = $state;
                        $context = '';

                        if (!$record->parent_invoice_id) {
                            // Main invoice
                            $context = match($state) {
                                'Draft' => 'Ready to Forward',
                                'Issued' => 'Chain Started',
                                'Closed' => 'Chain Ended',
                                default => $state
                            };
                        } else {
                            // Child invoice
                            $context = match($state) {
                                'Draft' => 'Can Continue/End',
                                'Issued' => 'Chain Continued',
                                'Closed' => 'Chain Ended',
                                default => $state
                            };
                        }

                        return $context;
                    })
                    ->color(fn ($record) => match ($record->status) {
                        'Draft' => $record->parent_invoice_id ? 'warning' : 'gray',
                        'Issued' => 'info',
                        'Closed' => 'success',
                    })
                    ->sortable(),
            ])
            ->filters([
                Filter::make('invoice_date')
                    ->form([
                        DatePicker::make('invoice_from')
							// ->default(now()->startOfMonth()),
							->default(today()),
						DatePicker::make('invoice_until')
							// ->default(now()->endOfMonth()),
							->default(today()),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['invoice_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['invoice_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    }),
                SelectFilter::make('status')
                    ->options([
                        'Draft' => 'Draft',
                        'Issued' => 'Issued',
                        'Closed' => 'Closed',
                    ]),
                SelectFilter::make('chain_type')
                    ->label('Chain Type')
                    ->options([
                        'main' => 'Main Invoice',
                        'child' => 'Child Invoice',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['value'] === 'main',
                            fn (Builder $query): Builder => $query->whereNull('parent_invoice_id'),
                        )->when(
                            $data['value'] === 'child',
                            fn (Builder $query): Builder => $query->whereNotNull('parent_invoice_id'),
                        );
                    })
				])
            ->actions([
				ActionGroup::make([
					ViewAction::make()->tooltip('View data'),
					EditAction::make()->tooltip('Edit data'),
				])->icon('heroicon-m-pencil-square'),
				ActionGroup::make([
					Action::make('Issue')
						->label('Forward to Next Company')
						->icon('heroicon-o-forward')
						->tooltip('Create invoice for next company in the chain')
						->color('primary')
						->requiresConfirmation()
						->modalHeading('Forward to Next Company')
						->modalDescription(function ($record) {
							// Calculate current chain position
							$depth = 0;
							$current = $record;
							while ($current->parent_invoice_id) {
								$depth++;
								$current = $current->parentInvoice;
							}
							$currentLevel = $depth + 1;
							$nextLevel = $currentLevel + 1;

							return "Current Position: Chain Level {$currentLevel}\n" .
								   "This will create an invoice for the next company (Chain Level {$nextLevel}) and mark the current invoice as issued.";
						})
						->modalSubmitActionLabel('Yes, Forward')
						->visible(function ($record) {
							return $record->status === 'Draft' && !$record->parent_invoice_id;
						})
						->disabled(function ($record) {
							return is_null($record->invoice_no);
						})
						->form([
							// memilih perusahaan yang akan menjadi client di invoice berikutnya
							Select::make('client_id')
								->label('Next Company in Chain')
								->helperText('Select the company that will receive funds from the current company')
								->options(function ($record) {
									return \App\Models\Company::whereNotIn('id', [
										$record->company_id, // Hindari company asal
										$record->client_id,  // Hindari client yang sama (kalau sudah ada)
									])->pluck('name', 'id');
								})
								->searchable()
								->required(),
						])
						->action(function (Invoice $record, array $data) {
							try {
								$invoice = DB::transaction(function () use ($record, $data) {
									$record->update(['status' => 'Issued']);

									//di invoice baru, client yang dipilih akan menjadi client di invoice baru
									$client = Company::find($data['client_id']);

									//di invoice baru, client lama akan menjadi company di invoice baru
									$company = Company::find($record->client_id);

									//jika ingin menggunakan dari Master
									// $detail = MasterInvoiceDetail::where('business_type', $company->business_type)->get();

									//jika ingin menggunakan dari detail
									$detail = InvoiceDetail::where('invoice_id', $record->id)->get();

									$bank = CompanyBank::where('company_id', $company->id)->where('is_default', true)->first();
									if(!$bank)
									{
										$bank = CompanyBank::where('company_id', $company->id)->first();
									}
									$invoice = Invoice::create([
										'order_id' => $record->order_id,
										'client_id' => $client->id,
										'company_id' => $company->id,
										'booking_fee' => $record->order?->booking_fee,
										'rates' => $record->rates,
										'order_amount' => $record->order_amount,
										'invoice_amount' => $record->total,
										'status' => 'Draft',
										'invoice_date'=> $record->invoice_date,
										'due_date' => $record->due_date,
										'invoice_create_by' => Auth::user()->id,
										'currency_id' => $record->currency_id,
										//include client address (sesuai client address)
										'client_address' => $client->address,
										'parent_invoice_id' => $record->id,
										'prev_invoice' => $record->invoice_no,

										//dan bank information (sesuai company bank)
										'bank_acc_name' => $bank?->bank_acc_name,
										'bank_code' => $bank?->bank_code,
										'bank_acc_no' => $bank?->bank_acc_no,
										'bank_acc_address' => $bank?->bank_acc_address,
										'bank_name' => $bank?->bank_name,
										'bank_address' => $bank?->bank_address,
										'bank_correspondent' => $bank?->bank_correspondent,
										'swift' => $bank?->swift,
										'swift_correspondent' => $bank?->swift_correspondent,
										'routing_no' => $bank?->routing_no,
										'transit' => $bank?->transit,
										'tt_charge' => $bank?->tt_charge,
										'iban' => $bank?->iban,
										'institution' => $bank?->institution,
										'bsb' => $bank?->bsb,
										'branch_code' => $bank?->branch_code,
										'sort_code' => $bank?->sort_code,
										'branch_bank' => $bank?->branch_bank,
										'back2back' => $bank?->back2back,
										'ABA' => $bank?->ABA,
										'IFSC' => $bank?->IFSC,
										'bank_custom_columns' => $bank?->custom_columns,
									]);

									// Tambahkan detail invoice dari master invoice detail sesuai business type company yang mengeluarkan invoice
									if ($detail->isNotEmpty()) {
										foreach ($detail as $item) {
											$invoice->invoiceDetails()->create([
												'company_id' => $data['client_id'],
												'client_id' => $record->company_id,
												'description' => $item->description,
												'quantity' => $item->quantity,
												'unit' => $item->unit,
												'price' => $item->price,
												'sub_total' => $item->quantity * $item->price,
												'status' => 'Active',
											]);
										}

										// Hitung ulang total invoice
										$total = $invoice->invoiceDetails()->sum('sub_total');
										$invoice->invoice_amount = $total;
										$invoice->inv_sub_total = $total;
										$invoice->save();
									}

									return $invoice;
								});

								Notification::make()
									->title('Invoice forwarded successfully.')
									->body('Invoice has been forwarded to the next company in the chain.')
									->success()
									->send();

								// Redirect ke halaman edit invoice yang baru dibuat
								return redirect()->route('filament.admin.resources.invoices.edit', ['record' => $invoice->id]);
							} catch (\Throwable $e) {
								Notification::make()
									->title('Failed to forward invoice.')
									->body('Error: ' . $e->getMessage())
									->danger()
									->send();

								// Tidak redirect, tetap di halaman ini
							}
						}),

					// Action for Child Invoices (Continue Chain)
					Action::make('continue_chain')
						->label('Continue Chain')
						->icon('heroicon-o-forward')
						->tooltip('Continue to next company in the chain')
						->color(fn ($record) => $record->invoice_no ? 'success' : '')
						->requiresConfirmation()
						->modalHeading('Continue Transaction Chain')
						->modalDescription(function ($record) {
							// Calculate current chain position
							$depth = 1;
							$current = $record;
							while ($current->parent_invoice_id) {
								$depth++;
								$current = $current->parentInvoice;
							}
							$currentLevel = $depth;
							$nextLevel = $currentLevel + 1;

							return "Current Position: Chain Level {$currentLevel}\n" .
								   "This will create an invoice for the next company (Chain Level {$nextLevel}) and mark the current invoice as issued.";
						})
						->modalSubmitActionLabel('Yes, Continue Chain')
						->visible(function ($record) {
							// Only show for Draft child invoices
							return $record->status === 'Draft' && $record->parent_invoice_id;
						})
						->disabled(function ($record) {
							return is_null($record->invoice_no); // true jika invoice_no masih kosong
						})
						->form([
							Select::make('client_id')
								->label('Next Company in Chain')
								->helperText('Select the company that will receive funds from the current company')
								->options(function ($record) {
									return \App\Models\Company::whereNotIn('id', [
										$record->company_id, // Hindari company asal
										$record->client_id,  // Hindari client yang sama (kalau sudah ada)
									])->pluck('name', 'id');
								})
								->searchable()
								->required(),
						])
						->action(function (Invoice $record, array $data) {
							try {
								$invoice = DB::transaction(function () use ($record, $data) {
									$record->update(['status' => 'Issued']);

									//di invoice baru, client yang dipilih akan menjadi client di invoice baru
									$client = Company::find($data['client_id']);

									//di invoice baru, client lama akan menjadi company di invoice baru
									$company = Company::find($record->client_id);

									//jika ingin menggunakan dari Master
									// $detail = MasterInvoiceDetail::where('business_type', $company->business_type)->get();

									//jika ingin menggunakan dari detail
									$detail = InvoiceDetail::where('invoice_id', $record->id)->get();

									$bank = CompanyBank::where('company_id', $company->id)->where('is_default', true)->first();
									if(!$bank)
									{
										$bank = CompanyBank::where('company_id', $company->id)->first();
									}
									$invoice = \App\Models\Invoice::create([
										'order_id' => $record->order_id,
										'client_id' => $client->id,
										'company_id' => $company->id,
										'booking_fee' => $record->order?->booking_fee,
										'rates' => $record->rates,
										'order_amount' => $record->order_amount,
										'invoice_amount' => $record->total,
										'status' => 'Draft',
										'invoice_date'=> $record->invoice_date,
										'due_date' => $record->due_date,
										'invoice_create_by' => Auth::user()->id,
										'currency_id' => $record->currency_id,
										//include client address (sesuai client address)
										'client_address' => $client->address,
										'parent_invoice_id' => $record->id,
										'prev_invoice' => $record->invoice_no,

										//dan bank information (sesuai company bank)
										'bank_acc_name' => $bank->bank_acc_name,
										'bank_code' => $bank->bank_code,
										'bank_acc_no' => $bank->bank_acc_no,
										'bank_acc_address' => $bank->bank_acc_address,
										'bank_name' => $bank->bank_name,
										'bank_address' => $bank->bank_address,
										'bank_correspondent' => $bank->bank_correspondent,
										'swift' => $bank->swift,
										'swift_correspondent' => $bank->swift_correspondent,
										'routing_no' => $bank->routing_no,
										'transit' => $bank->transit,
										'tt_charge' => $bank->tt_charge,
										'iban' => $bank->iban,
										'institution' => $bank->institution,
										'bsb' => $bank->bsb,
										'branch_code' => $bank->branch_code,
										'sort_code' => $bank->sort_code,
										'branch_bank' => $bank->branch_bank,
										'back2back' => $bank->back2back,
										'ABA' => $bank->ABA,
										'IFSC' => $bank->IFSC,
										'bank_custom_columns' => $bank->custom_columns,
									]);

									// Tambahkan detail invoice dari master invoice detail sesuai business type company yang mengeluarkan invoice
									if ($detail->isNotEmpty()) {
										foreach ($detail as $item) {
											$invoice->invoiceDetails()->create([
												'company_id' => $data['client_id'],
												'client_id' => $record->company_id,
												'description' => $item->description,
												'quantity' => $item->quantity,
												'unit' => $item->unit,
												'price' => $item->price,
												'sub_total' => $item->quantity * $item->price,
												'status' => 'Active',
											]);
										}

										// Hitung ulang total invoice
										$total = $invoice->invoiceDetails()->sum('sub_total');
										$invoice->invoice_amount = $total;
										$invoice->inv_sub_total = $total;
										$invoice->save();
									}

									return $invoice;
								});

								Notification::make()
									->title('Invoice forwarded successfully.')
									->body('Invoice has been forwarded to the next company in the chain.')
									->success()
									->send();

								// Redirect ke halaman edit invoice yang baru dibuat
								return redirect()->route('filament.admin.resources.invoices.edit', ['record' => $invoice->id]);
							} catch (\Throwable $e) {
								Notification::make()
									->title('Failed to continue chain.')
									->body('Error: ' . $e->getMessage())
									->danger()
									->send();

								// Tidak redirect, tetap di halaman ini
							}
						}),

					Action::make('Close')
						->label('End Chain Here')
						->icon('heroicon-o-lock-closed')
						->tooltip('End the transaction chain at this company')
						->color(fn ($record) => $record->invoice_no ? 'success' : '')
						->requiresConfirmation()
						->modalHeading('End Transaction Chain')
						->modalDescription(function ($record) {
							// Calculate current chain position
							$depth = 1;
							$current = $record;
							while ($current->parent_invoice_id) {
								$depth++;
								$current = $current->parentInvoice;
							}

							return "Current Position: Chain Level {$depth}\n" .
								   "This will end the transaction chain at this company and mark the invoice as closed. No further invoices will be created after this chain.";
						})
						->modalSubmitActionLabel('Yes, End Chain')
						->visible(function (Invoice $record) {
							return ($record->status === 'Draft');
						})
						->disabled(function ($record) {
							return is_null($record->invoice_no); // true jika invoice_no masih kosong
						})
						->action(function (Invoice $record) {
							try {

								// Refresh record dari database untuk memastikan data terbaru
								$record->refresh();

								// Update status
								$updated = $record->update(['status' => 'Closed']);

								if ($updated) {
									Notification::make()
										->title('Transaction chain ended successfully.')
										->body("Invoice #{$record->id} - Transaction chain has been ended at this company.")
										->success()
										->send();
								} else {
									throw new \Exception('Failed to update invoice status');
								}

							} catch (\Throwable $e) {
								Log::error('Failed to close invoice', [
									'invoice_id' => $record->id,
									'error' => $e->getMessage(),
									'trace' => $e->getTraceAsString()
								]);

								Notification::make()
									->title('Failed to end transaction chain.')
									->body('Error: ' . $e->getMessage())
									->danger()
									->send();
							}
						}),
					Action::make('printPreview')
						->label('Print Preview')
						->icon('heroicon-o-printer')
						// ->iconButton()
						->color('success')
						->tooltip('Print Preview')
						->url(fn (Invoice $record) => route('printInvoice', $record))
						->openUrlInNewTab(),
					Action::make('pdfPreview')
						->label('PDF Preview')
						->icon('icon-filetype-pdf')
						// ->iconButton()
						->color('warning')
						->tooltip('PDF Preview')
						->url(fn (Invoice $record) => route('printInvoicePdf', $record))
						->openUrlInNewTab(),
					// Action::make('pdfShiftPreview')
					// 	->label('PDF Preview (PDFShift)')
					// 	->icon('heroicon-o-cloud')
					// 	// ->iconButton()
					// 	->color('info')
					// 	->tooltip('PDF Preview using PDFShift API')
					// 	->url(fn (Invoice $record) => route('printInvoiceApi2Pdf', $record))
					// 	->openUrlInNewTab(),
				])->icon('heroicon-m-cog-6-tooth'),
                Action::make('verify')
                    ->label('Verify')
                    ->icon(fn ($record) => match ($record->verification_status) {
                        '0' => 'heroicon-o-hand-thumb-up',
                        '1' => 'heroicon-s-hand-thumb-up',
                        '2' => 'heroicon-s-hand-thumb-down',
                        default => 'heroicon-o-hand-thumb-up',
                    })
                    ->iconButton()
                    ->tooltip('Verify Invoice (First Level)')
                    ->color(fn ($record) => match ($record->verification_status) {
                        '0' => 'gray',
                        null => 'gray',
                        '1' => 'success',
                        '2' => 'danger',
                        default => 'gray'
                    })
					// ->visible(fn ($record) => $record->verification_status !== null)
                    ->disabled(fn () => !Auth::user()->hasRole('Verifier')) // Commented for single role
                    ->disabled(false) // Verification always available (first level)
                    ->requiresConfirmation()
                    ->modalHeading('Verification Action')
                    ->modalDescription('Choose your verification decision for this invoice.')
                    ->form([
                        Select::make('decision')
                            ->label('Decision')
                            ->options([
                                '1' => 'Approve',
                                '2' => 'Reject',
                            ])
                            ->required(),
                        Textarea::make('reason')
                            ->label('Reason/Notes')
                            ->placeholder('Optional: Add reason for your decision')
                            ->rows(3),
                    ])
                    ->action(function (Invoice $record, array $data) {
                        try {
                            $oldStatus = $record->verification_status;
                            $newStatus = $data['decision'];

                            // Update invoice status
                            $record->update([
                                'verification_status' => $newStatus,
                                'verified_by' => Auth::id(),
                            ]);

                            // Create audit log
                            InvoiceApprovalLog::create([
                                'invoice_id' => $record->id,
                                'user_id' => Auth::id(),
                                'creator_id' => $record->invoice_create_by,
                                'approval_type' => 'verification',
                                'old_status' => $oldStatus,
                                'new_status' => $newStatus,
                                'reason' => $data['reason'] ?? null,
                                'metadata' => [
                                    'invoice_no' => $record->invoice_no,
                                    'company_name' => $record->company?->name,
                                ],
                            ]);

                            $action = $newStatus === '1' ? 'approved' : 'rejected';
                            Notification::make()
                                ->title("Invoice verification {$action}")
                                ->body("Invoice #{$record->invoice_no} has been {$action}")
                                ->success()
                                ->send();

                        } catch (\Exception $e) {
                            Notification::make()
                                ->title('Verification failed')
                                ->body('Error: ' . $e->getMessage())
                                ->danger()
                                ->send();
                        }
                    }),
                Action::make('supervise')
                    ->label('Supervision')
                    ->icon(fn ($record) => match ($record->supervision_status) {
                        '0' => 'heroicon-o-hand-thumb-up',
                        '1' => 'heroicon-s-hand-thumb-up',
                        '2' => 'heroicon-s-hand-thumb-down',
                        default => 'heroicon-o-hand-thumb-up',
                    })
                    ->iconButton()
                    ->color(fn ($record) => match ($record->supervision_status) {
                        '0' => 'gray',
                        null => 'gray',
                        '1' => 'success',
                        '2' => 'danger',
                        default => 'gray'
                    })
					->visible(fn ($record) => $record->verification_status !== null)
                    ->disabled(fn () => !Auth::user()->hasRole('supervisor')) // Commented for single role
                    ->disabled(fn ($record) => $record->verification_status !== '1') // Only after verification approved
                    ->tooltip(fn ($record) =>
                        $record->verification_status !== '1'
                            ? 'Verification must be approved first'
                            : 'Supervise Invoice'
                    )
                    ->requiresConfirmation()
                    ->modalHeading('Supervision Action')
                    ->modalDescription('Choose your supervision decision for this invoice.')
                    ->form([
                        Select::make('decision')
                            ->label('Decision')
                            ->options([
                                '1' => 'Approve',
                                '2' => 'Reject',
                            ])
                            ->required(),
                        Textarea::make('reason')
                            ->label('Reason/Notes')
                            ->placeholder('Optional: Add reason for your decision')
                            ->rows(3),
                    ])
                    ->action(function (Invoice $record, array $data) {
                        try {
                            $oldStatus = $record->supervision_status;
                            $newStatus = $data['decision'];

                            // Update invoice status
                            $record->update([
                                'supervision_status' => $newStatus,
                                'supervised_by' => Auth::id(),
                            ]);

                            // Create audit log
                            InvoiceApprovalLog::create([
                                'invoice_id' => $record->id,
                                'user_id' => Auth::id(),
                                'creator_id' => $record->invoice_create_by,
                                'approval_type' => 'supervision',
                                'old_status' => $oldStatus,
                                'new_status' => $newStatus,
                                'reason' => $data['reason'] ?? null,
                                'metadata' => [
                                    'invoice_no' => $record->invoice_no,
                                    'company_name' => $record->company?->name,
                                ],
                            ]);

                            $action = $newStatus === '1' ? 'approved' : 'rejected';
                            Notification::make()
                                ->title("Invoice supervision {$action}")
                                ->body("Invoice #{$record->invoice_no} has been {$action}")
                                ->success()
                                ->send();

                        } catch (\Exception $e) {
                            Notification::make()
                                ->title('Supervision failed')
                                ->body('Error: ' . $e->getMessage())
                                ->danger()
                                ->send();
                        }
                    }),
                Action::make('approval')
                    ->label('Approval')
                    ->icon(fn ($record) => match ($record->approval_status) {
                        '0' => 'heroicon-o-hand-thumb-up',
                        '1' => 'heroicon-s-hand-thumb-up',
                        '2' => 'heroicon-s-hand-thumb-down',
                        default => 'heroicon-o-hand-thumb-up',
                    })
                    ->iconButton()
                    ->color(fn ($record) => match ($record->approval_status) {
                        '0' => 'gray',
                        null => 'gray',
                        '1' => 'success',
                        '2' => 'danger',
                        default => 'gray'
                    })
					->visible(fn ($record) => $record->supervision_status !== null)
                    ->disabled(fn () => !Auth::user()->hasRole('manager')) // Commented for single role
                    ->disabled(fn ($record) => $record->supervision_status !== '1') // Only after supervision approved
                    ->tooltip(fn ($record) =>
                        $record->supervision_status !== '1'
                            ? 'Supervision must be approved first'
                            : 'Final Approval'
                    )
                    ->requiresConfirmation()
                    ->modalHeading('Final Approval Action')
                    ->modalDescription('Choose your final approval decision for this invoice.')
                    ->form([
                        Select::make('decision')
                            ->label('Decision')
                            ->options([
                                '1' => 'Approve',
                                '2' => 'Reject',
                            ])
                            ->required(),
                        Textarea::make('reason')
                            ->label('Reason/Notes')
                            ->placeholder('Optional: Add reason for your decision')
                            ->rows(3),
                    ])
                    ->action(function (Invoice $record, array $data) {
                        try {
                            $oldStatus = $record->approval_status;
                            $newStatus = $data['decision'];

                            // Update invoice status
                            $record->update([
                                'approval_status' => $newStatus,
                                'approved_by' => Auth::id(),
                            ]);

                            // Create audit log
                            InvoiceApprovalLog::create([
                                'invoice_id' => $record->id,
                                'user_id' => Auth::id(),
                                'creator_id' => $record->invoice_create_by,
                                'approval_type' => 'approval',
                                'old_status' => $oldStatus,
                                'new_status' => $newStatus,
                                'reason' => $data['reason'] ?? null,
                                'metadata' => [
                                    'invoice_no' => $record->invoice_no,
                                    'company_name' => $record->company?->name,
                                ],
                            ]);

                            $action = $newStatus === '1' ? 'approved' : 'rejected';
                            Notification::make()
                                ->title("Invoice final approval {$action}")
                                ->body("Invoice #{$record->invoice_no} has been {$action}")
                                ->success()
                                ->send();

                        } catch (\Exception $e) {
                            Notification::make()
                                ->title('Final approval failed')
                                ->body('Error: ' . $e->getMessage())
                                ->danger()
                                ->send();
                        }
                    }),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
}
