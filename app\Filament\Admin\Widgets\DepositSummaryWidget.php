<?php

namespace App\Filament\Admin\Widgets;

use App\Models\Company;
use App\Models\CompanyDepositSummary;
use Filament\Forms\Components\Select;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Filament\Tables\View\TablesRenderHook;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Number;

class DepositSummaryWidget extends BaseWidget
{
    protected static ?string $heading = 'Balance Summary by Company';
    protected int | string | array $columnSpan = 1;

    public array $selectedCompanyIds = [];

    protected $listeners = ['order-created' => '$refresh'];

    public function mount(): void
    {
        // Initialize with empty selection to show default data
        if (empty($this->selectedCompanyIds)) {
            $this->selectedCompanyIds = [];
        }
    }


	public static function canView(): bool
    {
        return Auth::user()->hasAnyRole(['Staff Order', 'Admin', 'Super Admin']);
    }
	public static function canAccess(): bool
    {
        return Auth::user()->hasAnyRole(['Staff Order', 'Admin', 'Super Admin']);
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(Company::query())
            ->modifyQueryUsing(function (Builder $query) {
                $query->where('type', 2)
                    ->whereRaw("TRIM(name) LIKE ?", ['PT%'])
                    ->leftJoin('company_banks', 'companies.id', '=', 'company_banks.company_id')
                    ->leftJoin('view_company_deposit_summary', 'company_banks.id', '=', 'view_company_deposit_summary.bank_id')
                    ->select([
                        'companies.id',
                        'companies.name',
                        'companies.logo',
                        'companies.address',
                        DB::raw('COALESCE(SUM(view_company_deposit_summary.balance), 0) as total_balance'),
                        DB::raw('COALESCE(SUM(view_company_deposit_summary.total_in), 0) as total_in'),
                        DB::raw('COALESCE(SUM(view_company_deposit_summary.total_out), 0) as total_out'),
                        DB::raw('COALESCE(SUM(view_company_deposit_summary.total_order), 0) as total_order'),
                        DB::raw('COUNT(company_banks.id) as bank_count')
                    ])
                    ->groupBy('companies.id', 'companies.name', 'companies.logo', 'companies.address');

                if (!empty($this->selectedCompanyIds)) {
                    $query->whereIn('companies.id', $this->selectedCompanyIds);
                }
            })
            ->columns([
                TextColumn::make('name')
                    ->label('Company')
                    ->sortable()
                    ->searchable()
                    ->limit(15),
                TextColumn::make('total_balance')
                    ->label('Total Balance')
					->alignEnd()
                    ->money('IDR')
                    ->sortable()
                    ->color(fn ($state) => ($state ?? 0) >= 0 ? 'success' : 'danger')
                    ->weight('bold')
                    ->default(0),
            ])
            ->actions([
                Action::make('view_details')
                    ->label('Details')
                    ->icon('heroicon-o-eye')
                    ->iconButton()
                    ->tooltip('View Bank Details')
                    ->modalHeading(fn ($record) => "Bank Details: {$record->name}")
                    ->modalContent(function ($record) {
                        // Get all banks for this company with their deposit summary
						$banks = Cache::remember("company_banks_summary_{$record->id}", now()->addMinutes(10), function () use ($record) {
							return \App\Models\CompanyBank::withDepositSummary()
								->where('company_id', $record->id)
								->get();
						});

                        $totalIn = number_format($record->total_in ?? 0, 2, ',', '.');
                        $totalOut = number_format($record->total_out ?? 0, 2, ',', '.');
                        $totalOrder = number_format($record->total_order ?? 0, 2, ',', '.');
                        $totalBalance = number_format($record->total_balance ?? 0, 2, ',', '.');
                        $balanceColor = ($record->total_balance ?? 0) >= 0 ? 'text-green-600' : 'text-red-600';

                        $bankDetails = '';
                        foreach ($banks as $bank) {
                            $bankBalance = number_format($bank->balance ?? 0, 2, ',', '.');
                            $bankBalanceColor = ($bank->balance ?? 0) >= 0 ? 'text-green-600' : 'text-red-600';
                            $bankIn = number_format($bank->total_in ?? 0, 2, ',', '.');
                            $bankOut = number_format($bank->total_out ?? 0, 2, ',', '.');
                            $bankOrder = number_format($bank->total_order ?? 0, 2, ',', '.');

                            $bankDetails .= "
                                <div class='border rounded-lg p-2 bg-white dark:bg-transparent'>
                                    <div class='flex justify-between items-center mb-3'>
                                        <h5 class='font-semibold text-gray-800 dark:text-gray-300'>{$bank->bank_name}</h5>
                                        <span class='text-lg font-bold {$bankBalanceColor}'>IDR {$bankBalance}</span>
                                    </div>
                                    <div class='grid grid-cols-2 gap-2 text-xs'>
										<div class='col-span-1 w-full'>
											<table class='w-full'>
												<tr>
													<td class='align-top' width='25%'>Account:</td>
													<td width='75%' class='font-bold'>{$bank->bank_acc_no}</td>
												</tr>
												<tr>
													<td class='align-top'>Acc Name:</td>
													<td class='font-bold'>{$bank->bank_acc_name}</td>
												</tr>
											</table>
										</div>
										<div class='col-span-1 w-full'>
											<table class='w-full'>
												<tr>
													<td width='35%'>Total In:</td>
													<td width='60%' class='text-end'>IDR {$bankIn}</td>
												</tr>
												<tr>
													<td>Total Out:</td>
													<td class='text-end'>IDR {$bankOut}</td>
												</tr>
												<tr>
													<td>Total Order:</td>
													<td class='text-end'>IDR {$bankOrder}</td>
												</tr>
												<tr>
													<td class='font-bold border-t-2'>Balance:</td>
													<td class='{$bankBalanceColor} text-end font-bold border-t-2'>IDR {$bankBalance}</td>
												</tr>
											</table>
										</div>
                                    </div>
                                </div>
                            ";
                        }

                        return new HtmlString("
                            <div class='space-y-6'>
                                <!-- Company Summary -->
                                <div class='bg-gray-50 dark:bg-transparent p-4 rounded-lg'>
                                    <h4 class='font-semibold text-gray-800 dark:text-gray-300 mb-3'>Summary</h4>
                                    <div class='grid grid-cols-2 gap-4'>
                                        <div class='bg-blue-50 dark:bg-transparent border border-gray-50 dark:border-primary-500 p-3 rounded'>
                                            <h5 class='font-medium text-blue-800 dark:text-gray-300'>Total In</h5>
                                            <p class='text-lg font-bold text-blue-600 dark:text-gray-300'>IDR {$totalIn}</p>
                                        </div>
                                        <div class='bg-red-50 dark:bg-transparent border border-gray-50 dark:border-red-500 p-3 rounded'>
                                            <h5 class='font-medium text-red-800 dark:text-gray-300'>Total Out</h5>
                                            <p class='text-lg font-bold text-red-600 dark:text-gray-300'>IDR {$totalOut}</p>
                                        </div>
                                        <div class='bg-yellow-50 dark:bg-transparent border border-gray-50 dark:border-yellow-500 p-3 rounded'>
                                            <h5 class='font-medium text-yellow-800 dark:text-gray-300'>Total Orders</h5>
                                            <p class='text-lg font-bold text-yellow-600 dark:text-gray-300'>IDR {$totalOrder}</p>
                                        </div>
                                        <div class='bg-gray-100 dark:bg-transparent border border-gray-50 dark:border-green-500 p-3 rounded'>
                                            <h5 class='font-medium text-gray-800 dark:text-gray-300'>Total Balance</h5>
                                            <p class='text-lg font-bold {$balanceColor}  dark:text-gray-300'>IDR {$totalBalance}</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Bank Details -->
                                <div>
                                    <h4 class='font-semibold text-gray-800 dark:text-gray-300 mb-3'>Bank Details ({$record->bank_count})</h4>
                                    <div class='space-y-3'>
                                        {$bankDetails}
                                    </div>
                                </div>
                            </div>
                        ");
                    })
                    ->modalWidth('2xl')
                    ->modalSubmitAction(false)
                    ->modalCancelActionLabel('Close'),
            ])
            // ->headerActions([
            //     Action::make('filter_company')
            //         ->label('Filter')
            //         ->icon('heroicon-o-funnel')
            //         ->form([
            //             Select::make('company_ids')
            //                 ->label('Select Companies')
            //                 ->options(Company::where('type', 2)->where('name', 'like', 'PT%')->pluck('name', 'id'))
            //                 ->placeholder('Select companies...')
            //                 ->multiple()
            //                 ->searchable()
            //                 ->required()
            //         ])
            //         ->action(function (array $data) {
            //             $this->selectedCompanyIds = $data['company_ids'] ?? [];
            //         })
            //         ->modalHeading('Filter Companies')
            //         ->modalSubmitActionLabel('Apply'),
            //     Action::make('clear_filter')
            //         ->label('Clear')
            //         ->icon('heroicon-o-x-mark')
            //         ->color('gray')
            //         ->action(function () {
            //             $this->selectedCompanyIds = [];
            //         })
            //         ->visible(fn () => !empty($this->selectedCompanyIds)),
            // ])
            ->defaultSort('total_balance', 'desc')
            ->striped()
            ->paginated([6])
            ->defaultPaginationPageOption(3)
            ->emptyStateHeading('No Data Available')
            ->emptyStateDescription('Select companies from filter or check if data exists.');
    }


	public function getTableRenderHooks(): array
    {
        return [
            TablesRenderHook::HEADER_BEFORE => fn () => null,
            TablesRenderHook::HEADER_AFTER => fn () => null,
        ];
    }
}
