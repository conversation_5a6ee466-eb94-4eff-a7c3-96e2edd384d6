<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Invoice extends Model
{
	use HasFactory, SoftDeletes;

	protected $table = 'invoices';

	protected $fillable = [
		'order_id',
		'company_id',
		'client_id',
		'parent_invoice_id',
		'prev_invoice',
		'invoice_create_by',
		'invoice_no',
		'invoice_date',
		'due_date',
		'currency_id',
		'order_amount',
		'booking_fee',
		'invoice_amount',
		'status',
		'inv_sub_total',
		'rates',
		'amount_inword',
		'client_address',

		//bank info
		'bank_acc_name',
		'bank_code',
		'bank_acc_no',
		'bank_acc_address',
		'bank_name',
		'bank_address',
		'bank_correspondent',
		'swift',
		'swift_correspondent',
		'remarks',
		'routing_no',
		'transit',
		'tt_charge',
		'iban',
		'institution',
		'bsb',
		'branch_code',
		'sort_code',
		'branch_bank',
		'back2back',
		'ABA',
		'IFSC',
		'bank_custom_columns',

		//verification
		'verification_status',
		'verified_by',
		'supervision_status',
		'supervised_by',
		'approval_status',
		'approved_by',
	];

	protected $casts = [
		'bank_custom_columns' => 'array',
	];

	protected static function boot()
	{
		parent::boot();

		// Global scope for ordering
		static::addGlobalScope('order', function ($builder) {
			$builder->orderBy('created_at', 'desc');
		});

		// Initialize approval workflow status for new invoices
		static::creating(function ($invoice) {
			// Set default approval statuses to pending
			$invoice->verification_status = '0';	// Pending
			$invoice->supervision_status = '0';		// Pending
			$invoice->approval_status = '0';		// Pending
			$invoice->status = 'Draft';				// Pending
		});

		// Handle cascade deletion when invoice is deleted
		static::deleting(function ($invoice) {
			// Hard delete related invoice details (no audit trail needed)
			$invoice->invoiceDetails()->forceDelete();

			// Update related order status back to 'Forwarded' if exists and no other active invoices
			if ($invoice->order_id && $invoice->order) {
				// Check if there are other active invoices for this order
				$remainingInvoices = $invoice->order->invoices()
					->where('id', '!=', $invoice->id)
					->whereNull('deleted_at')
					->count();

				// Only update status to 'Forwarded' if no other active invoices remain
				if ($remainingInvoices === 0) {
					$invoice->order->update(['status' => 'Forwarded']);
				}
			}
		});
	}

	// Relationship --

		public function company()
		{
			return $this->belongsTo(Company::class, 'company_id', 'id');
		}

		public function parentInvoice()
		{
			return $this->belongsTo(Invoice::class, 'parent_invoice_id', 'id');
		}

		public function childInvoice()
		{
			return $this->hasMany(Invoice::class, 'parent_invoice_id', 'id');
		}

		public function creator()
		{
			return $this->belongsTo(User::class, 'invoice_create_by', 'id');
		}

		public function order()
		{
			return $this->belongsTo(Order::class, 'order_id', 'id');
		}

		public function invoiceDetails()
		{
			return $this->hasMany(InvoiceDetail::class, 'invoice_id', 'id');
		}

		public function companyBanks()
		{
			return $this->hasMany(CompanyBank::class, 'company_id', 'company_id')
						->where('include_in_invoice', true);
		}

		public function allCompanyBanks()
		{
			return $this->hasMany(CompanyBank::class, 'company_id', 'company_id');
		}

		public function invoicetemplate()
		{
			return $this->hasOne(InvoiceTemplate::class, 'company_id', 'company_id');
		}

		public function client()
		{
			return $this->belongsTo(Company::class, 'client_id', 'id');
		}

		public function currency()
		{
			return $this->belongsTo(Currency::class, 'currency_id', 'id');
		}

		public function verifier()
		{
			return $this->belongsTo(User::class, 'verified_by', 'id');
		}

		public function supervisor()
		{
			return $this->belongsTo(User::class, 'supervised_by', 'id');
		}

		public function approver()
		{
			return $this->belongsTo(User::class, 'approved_by', 'id');
		}
	// --
}
