<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Gross Profit Performance Report</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="{{ $fontSource }}" rel="stylesheet">
    @vite('resources/css/app.css')

    <style>
        @page {
            size: {{ $paperSize }};
            margin: {{ $marginTop }}mm {{ $marginRight }}mm {{ $marginBottom }}mm {{ $marginLeft }}mm;
        }

		@media print {
			.page-break {
				page-break-before: always;
			}
		}


        body {
            font-family: '{{ $fontFamily }}', sans-serif;
            font-size: 12px;
            color: #111827;
            line-height: 1.4;
        }

        th, td {
            border: 1px solid #d1d5db;
            padding: 6px 8px;
            text-align: left;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        thead {
            background-color: #f3f4f6;
            font-weight: 600;
        }

        .text-right {
            text-align: right;
        }

        .text-center {
            text-align: center;
        }

        .mb-4 {
            margin-bottom: 1rem;
        }

        .uppercase {
            text-transform: uppercase;
        }

        .font-bold {
            font-weight: bold;
        }
		@page {
			size: 355.6mm 215.9mm;
		}
    </style>
</head>
<body class=" text-xs">
	<div class="grid grid-cols-2 justify-between mb-4 items-center">
		<div class="col-span-1">
			<h2 class="text-xl font-bold uppercase">Gross Profit Performance Report</h2>
			<div>
				<span class="me-1">Start Date:</span>
				<span class="me-3 font-semibold">
					{{ $dateStart ? \Carbon\Carbon::parse($dateStart)->translatedFormat('d F Y') : 'Unfiltered' }}
				</span>

				<span class="me-1">to:</span>
				<span class="font-semibold">
					{{ $dateEnd ? \Carbon\Carbon::parse($dateEnd)->translatedFormat('d F Y') : 'Unfiltered' }}
				</span>
			</div>
		</div>
		<div class="col-span-1 text-end">
			{{-- <span>Report Date: {{ \Carbon\Carbon::today()->format('d F Y') }}</span> --}}
		</div>
	</div>

	<div class="grid grid-cols-3 justify-between items-center space-x-3 mb-6">
		<div class="col-span-1 rounded shadow p-4" style="background-color: #fffcf4">
			<span>Order Count</span>
			<h3 class="text-2xl font-semibold">{{ number_format($totalInvoices, 0, ',', '.') }}</h3>
		</div>
		<div class="col-span-1 rounded shadow p-4" style="background-color: #fff4f4">
			<span>Total Order</span>
			<h3 class="text-2xl font-semibold">Rp {{ number_format($totalAmount, 2, ',', '.') }}</h3>
		</div>
		<div class="col-span-1 rounded shadow p-4" style="background-color: #f4fffa">
			<span>Gross Profit</span>
			<h3 class="text-2xl font-semibold">Rp {{ number_format($totalProfit, 2, ',', '.') }}</h3>
		</div>
	</div>
	@if ($report)
		<div class="w-full space-y-4">
			<div class="grid grid-cols-2 gap-4">
				<div class="col-span-1">
					{{-- Line Chart (Daily Amount vs Gross Profit) --}}
					<div>
						<h3 class="font-semibold text-sm mb-2">Daily Transaction Amount</h3>
						<div class="p-2 w-full h-auto border rounded shadow bg-white">
							<svg viewBox="0 0 800 300" width="100%" height="auto">
								@php
									$dailyChart = collect($dailyChart);
									$amountMax = $dailyChart->pluck('amount')->max();
									$amountScaleY = 250 / ($amountMax * 1.1);
									$dates = array_keys($dailyChart->toArray());
									$xStep = count($dates) > 1 ? 750 / (count($dates) - 1) : 1;

									function formatKMB($val) {
										if ($val >= 1_000_000_000) return round($val / 1_000_000_000, 1) . 'B';
										if ($val >= 1_000_000) return round($val / 1_000_000, 1) . 'M';
										if ($val >= 1_000) return round($val / 1_000, 1) . 'K';
										return (string) $val;
									}
								@endphp

								{{-- Grid Y --}}
								@for ($i = 0; $i <= 5; $i++)
									@php
										$y = 300 - ($i * 50);
										$val = ($amountMax / 5) * $i;
									@endphp
									<line x1="0" x2="800" y1="{{ $y }}" y2="{{ $y }}" stroke="#e5e7eb" stroke-width="1" />
									<text x="5" y="{{ $y - 5 }}" font-size="10" fill="#6b7280">{{ formatKMB($val) }}</text>
								@endfor

								{{-- Line Chart --}}
								<polyline fill="none" stroke="#3b82f6" stroke-width="2" points="
									@foreach($dailyChart->values() as $index => $day)
										{{ $index * $xStep }},{{ 300 - ($day['amount'] * $amountScaleY) }}
									@endforeach
								" />

								{{-- Titik --}}
								@foreach($dailyChart->values() as $index => $day)
									<circle cx="{{ $index * $xStep }}" cy="{{ 300 - ($day['amount'] * $amountScaleY) }}" r="3" fill="#3b82f6" />
								@endforeach

								{{-- Label Tanggal --}}
								@foreach($dates as $index => $date)
									<text x="{{ $index * $xStep }}" y="330" font-size="10" text-anchor="middle" fill="#374151">
										{{ \Carbon\Carbon::parse($date)->format('d M') }}
									</text>
								@endforeach
							</svg>
						</div>
					</div>
				</div>
				<div class="col-span-1">
					{{-- Line Chart (Daily Amount vs Gross Profit) --}}
					<div>
						<h3 class="font-semibold text-sm mb-2">Daily Gross Profit</h3>
						<div class="p-2 w-full h-auto border rounded shadow bg-white">
							<svg viewBox="0 0 800 300" width="100%" height="auto">
								@php
									$profitMax = $dailyChart->pluck('gross_profit')->max();
									$profitScaleY = 250 / ($profitMax * 1.1);
								@endphp

								{{-- Grid Y --}}
								@for ($i = 0; $i <= 5; $i++)
									@php
										$y = 300 - ($i * 50);
										$val = ($profitMax / 5) * $i;
									@endphp
									<line x1="0" x2="800" y1="{{ $y }}" y2="{{ $y }}" stroke="#e5e7eb" stroke-width="1" />
									<text x="5" y="{{ $y - 5 }}" font-size="10" fill="#6b7280">{{ formatKMB($val) }}</text>
								@endfor

								{{-- Line Chart --}}
								<polyline fill="none" stroke="#10b981" stroke-width="2" stroke-dasharray="4,4" points="
									@foreach($dailyChart->values() as $index => $day)
										{{ $index * $xStep }},{{ 300 - ($day['gross_profit'] * $profitScaleY) }}
									@endforeach
								" />

								{{-- Titik --}}
								@foreach($dailyChart->values() as $index => $day)
									<circle cx="{{ $index * $xStep }}" cy="{{ 300 - ($day['gross_profit'] * $profitScaleY) }}" r="3" fill="#10b981" />
								@endforeach

								{{-- Label Tanggal --}}
								@foreach($dates as $index => $date)
									<text x="{{ $index * $xStep }}" y="330" font-size="10" text-anchor="middle" fill="#374151">
										{{ \Carbon\Carbon::parse($date)->format('d M') }}
									</text>
								@endforeach
							</svg>
						</div>
					</div>
				</div>
			</div>
			<div class="grid  grid-cols-2">
				<div class="col-span-1">
					{{-- Bar Chart (Total Amount per Nama PT) --}}
					<div>
						<h3 class="font-semibold text-sm mb-2">Total Transaction by Company</h3>
						<div class="p-2 w-full h-auto border rounded shadow bg-white">
							@php
								$barWidth = 40;
								$gap = 20;
								$maxBar = max($ptChart ?: [0]);
								$scaleBar = $maxBar > 0 ? 250 / ($maxBar * 1.1) : 1;
								$totalBars = count($ptChart);
								$chartWidth = ($barWidth + $gap) * $totalBars;

								// Warna dinamis (siklus ulang jika PT lebih banyak dari warna)
								$colors = ['#f97316', '#3b82f6', '#10b981', '#f43f5e', '#8b5cf6', '#ec4899', '#22d3ee', '#a3e635', '#eab308', '#6366f1'];
							@endphp
							<svg viewBox="0 0 800 300" width="100%" height="auto">
								@php
									$barWidth = 40;
									$gap = 20;
									$offsetLeft = 60; // agar tidak mentok label Y
									$maxBar = max($ptChart ?: [0]);
									$scaleBar = $maxBar > 0 ? 250 / ($maxBar * 1.1) : 1;
									$colors = ['#f97316', '#3b82f6', '#10b981', '#f43f5e', '#8b5cf6', '#ec4899', '#22d3ee', '#a3e635', '#eab308', '#6366f1'];

									// Fungsi pemformatan angka menjadi K/M/B
									function formatAbbreviated($num) {
										if ($num >= 1000000000) return round($num / 1000000000, 1) . 'B';
										if ($num >= 1000000)    return round($num / 1000000, 1) . 'M';
										if ($num >= 1000)       return round($num / 1000, 1) . 'K';
										return (string) $num;
									}
								@endphp

								{{-- Grid & Y-axis label --}}
								@for ($i = 0; $i <= 5; $i++)
									@php
										$y = 300 - ($i * 50);
										$value = ($maxBar / 5) * $i;
									@endphp
									<line x1="{{ $offsetLeft }}" x2="580" y1="{{ $y }}" y2="{{ $y }}" stroke="#e5e7eb" stroke-width="1" />
									<text x="{{ $offsetLeft - 10 }}" y="{{ $y - 5 }}" font-size="10" fill="#6b7280" text-anchor="end">
										{{ formatAbbreviated($value) }}
									</text>
								@endfor

								{{-- Bar chart --}}
								@foreach($ptChart as $pt => $amount)
									@php
										$index = $loop->index;
										$x = $offsetLeft + $index * ($barWidth + $gap);
										$height = $amount * $scaleBar;
										$y = 300 - $height;
										$color = $colors[$index % count($colors)];
									@endphp

									<rect x="{{ $x }}" y="{{ $y }}" width="{{ $barWidth }}" height="{{ $height }}" fill="{{ $color }}" />

									{{-- Rotated label nilai, di tengah batang --}}
									<text transform="rotate(-90, {{ $x + ($barWidth / 2) }}, {{ $y + ($height / 2) }})"
										x="{{ $x + ($barWidth / 2) }}" y="{{ $y + ($height / 2) }}"
										font-size="10" fill="white" text-anchor="middle">
										{{ formatAbbreviated($amount) }}
									</text>
								@endforeach

								{{-- Legenda kanan --}}
								@php
									$legendStartX = 620;
									$legendStartY = 40;
								@endphp

								@foreach($ptChart as $pt => $amount)
									@php
										$i = $loop->index;
										$y = $legendStartY + $i * 20;
										$color = $colors[$i % count($colors)];
									@endphp
									<rect x="{{ $legendStartX }}" y="{{ $y }}" width="10" height="10" fill="{{ $color }}" />
									<text x="{{ $legendStartX + 15 }}" y="{{ $y + 9 }}" font-size="10" fill="#374151">
										{{ Str::limit($pt, 20) }} ({{ formatAbbreviated($amount) }})
									</text>
								@endforeach
							</svg>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="page-break"></div>
	@endif

	<h3 class="font-semibold text-sm mb-2 w-full text-center underline-offset-8" style="text-underline-offset: 8pts">DAILY GROSS PROFIT REPORT</h3>
    <table class="invoice-table text-xs mb-6">
        <thead>
            <tr class="bg-blue-950 text-white">
                <th>#</th>
                <th>Invoice No</th>
                <th>Date</th>
                <th>Client</th>
                <th>Agent</th>
                <th class="text-right">Amount (Rp)</th>
                <th>Overseas Agent</th>
                <th>Cur.</th>
                <th class="text-right">Fee</th>
                <th class="text-right">Rate (Rp)</th>
                <th class="text-right">Gross Profit</th>
            </tr>
        </thead>
        <tbody>
			@forelse ($report as $i => $row)
				<tr class="{{ $row['bgColor'] }}">
					<td>{{ $i + 1 }}</td>
					<td>{{ $row['invoice_no'] }}</td>
					<td>{{ \Carbon\Carbon::parse($row['created_at'])->format('d/m/Y') }}</td>
					<td>{{ $row['client_name'] }}</td>
					<td>{{ $row['nama_pt_name'] }}</td>
					<td class="text-right">{{ number_format($row['amount'], 2, ',', '.') }}</td>
					<td>{{ $row['overseas_agent'] }}</td>
					<td class="text-center">{{ $row['currency'] }}</td>
					<td class="text-right">{{ number_format($row['fee_amount'], 2, ',', '.') }}</td>
					<td class="text-right">{{ number_format($row['rate'], 2, ',', '.') }}</td>
					<td class="text-right">{{ number_format($row['gross_profit'], 2, ',', '.') }}</td>
				</tr>
			@empty
				<tr>
					<td colspan="11" class="text-center py-4" style="color: #9c9c9c">
						No data available to display.
					</td>
				</tr>
			@endforelse
		</tbody>
        <tfoot>
            <tr class="border-t-2 border-t-slate-300">
                <td colspan="10" class="text-right font-medium">Invoice Count</td>
                <td class="text-right font-bold">{{ number_format($totalInvoices, 2, ',', '.') }}</td>
			</tr>
            <tr>
                <td colspan="10" class="text-right font-medium">Total Invoice Amount (Rp)</td>
                <td class="text-right font-bold">{{ number_format($totalAmount, 2, ',', '.') }}</td>
			</tr>
            <tr>
                <td colspan="10" class="text-right font-medium">Gained Profit (Rp)</td>
                <td class="text-right font-bold">{{ number_format($totalProfit, 2, ',', '.') }}</td>
			</tr>
    </table>
	<div class="text-xs flex gap-2 mt-6 items-center">
		<span class="inline-block w-5 h-5 bg-yellow-50 rounded-sm border border-slate-300"></span>
		<span class="ml-1 italic text-gray-200">Has no Order</span>
	</div>
</body>
</html>
