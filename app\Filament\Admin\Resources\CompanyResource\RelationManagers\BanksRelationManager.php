<?php

namespace App\Filament\Admin\Resources\CompanyResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use App\Models\CompanyBank;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Support\Enums\Alignment;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Support\HtmlString;

class BanksRelationManager extends RelationManager
{
    protected static string $relationship = 'banks';

	public static string | Alignment $formActionsAlignment = Alignment::Right;

    public function form(Form $form): Form
    {
        return $form
            ->schema([
				Placeholder::make('')->hiddenLabel()->helperText('Blank field(s) will not be displayed on the invoice.'),
				Toggle::make('is_default')->columnSpanFull()->label('Set as Default')
					->helperText('Make this Bank as default selection if this company has more than one bank.'),
                Fieldset::make('Bank Information')
					->columns(1)
					->extraAttributes(['class'=>'mt-6'])
					->schema([
						TextInput::make('bank_name')->inlineLabel()->label('Bank Name'),
						TextInput::make('bank_code')->inlineLabel()->label('Bank Code'),
						TextInput::make('branch_bank')->inlineLabel()->label('Branch'),
						TextInput::make('branch_code')->inlineLabel()->label('Branch Code'),
						TextInput::make('bank_correspondent')->inlineLabel()->label('Bank Correspondent'),
						RichEditor::make('bank_address')
							->extraInputAttributes(['style' => 'min-height: 5rem; max-height: 5vh; overflow-y: auto;'])
							->toolbarButtons([])->columnSpanFull()->inlineLabel(),
					]),

				Fieldset::make('Account Information')
					->columns(1)
					->extraAttributes(['class'=>'mt6'])
					->schema([
						TextInput::make('bank_acc_name')->inlineLabel()->label('Account Name'),
						TextInput::make('bank_acc_no')->inlineLabel()->label('Account Number'),
						RichEditor::make('bank_acc_address')
							->label('Account Address')
							->extraInputAttributes(['style' => 'min-height: 5rem; max-height: 5vh; overflow-y: auto;'])
							->toolbarButtons([])->columnSpanFull()->inlineLabel(),
					]),

                Fieldset::make('Swift')
                    ->columns(1)
                    ->extraAttributes(['class' => 'mt-6'])
                    ->schema([
                        RichEditor::make('swift')
                            ->label('Swift Code')
                            ->extraInputAttributes(['style' => 'min-height: 5rem; max-height: 5vh; overflow-y: auto;'])
                            ->toolbarButtons([])->columnSpanFull()->inlineLabel(),
                        TextInput::make('swift_correspondent')->inlineLabel()->label('Swift Correspondent'),
                    ]),

				Fieldset::make('Additional Information')
					->columns(1)
					->extraAttributes(['class'=>'mt-6'])
					->schema([
						TextInput::make('sort_code')->inlineLabel()->label('Sort Code'),
						TextInput::make('routing_no')->inlineLabel()->label('Routing No'),
						TextInput::make('transit')->inlineLabel()->label('Transit Code'),
						TextInput::make('tt_charge')->inlineLabel()->label('TT Charge'),
						TextInput::make('institution')->inlineLabel()->label('Institution Code'),
						TextInput::make('iban')->inlineLabel()->label('IBAN'),
						TextInput::make('bsb')->inlineLabel()->label('BSB'),
						TextInput::make('sort_code')->inlineLabel()->label('sort_code')->columnSpanFull(),
						TextInput::make('branch_bank')->inlineLabel()->label('branch_bank')->columnSpanFull(),
						TextInput::make('ABA')->inlineLabel()->label('ABA')->columnSpanFull(),
						TextInput::make('IFSC')->inlineLabel()->label('IFSC')->columnSpanFull(),
					]),

				Fieldset::make('Custom Fields (Optional)')
					->columns(1)
					->extraAttributes(['class' => 'mt-6'])
					->schema([
						Placeholder::make('custom_info')
							->hiddenLabel()
							->content('Add custom fields for additional bank information that is not covered by standard fields.'),

						Repeater::make('custom_columns_data')
							->hiddenLabel()
							->schema([
								TextInput::make('key')
									->label('Field Name')
									->required()
									->inlineLabel()
									->maxLength(20)
									->live(onBlur: true)
									->afterStateUpdated(function (callable $set, callable $get, $state) {
										if (!$state) {
											$set('duplicate_warning', null);
											return;
										}

										// Get delivered columns
										$deliveredColumns = CompanyBank::getDeliveredColumns();

										// Get existing custom keys from current form data
										$customColumnsData = $get('../../custom_columns_data') ?? [];

										// Count occurrences of current key
										$keyCount = 0;
										foreach ($customColumnsData as $item) {
											if (!empty($item['key']) && $item['key'] === $state) {
												$keyCount++;
											}
										}

										// Check for duplicates
										$isDuplicateDelivered = in_array($state, $deliveredColumns);
										$isDuplicateCustom = $keyCount > 1;

										if ($isDuplicateDelivered) {
											$set('value', null);
											$set('duplicate_warning', 'Field name conflicts with system field');
										} elseif ($isDuplicateCustom) {
											$set('value', null);
											$set('duplicate_warning', 'Field name already used in custom fields');
										} else {
											$set('duplicate_warning', null);
										}
									})
									->helperText(function ($get) {
										$warning = $get('duplicate_warning');
										return new HtmlString($warning ? '<span class="text-primary-500">'.$warning.'</span>' : 'Maximum 20 characters. Must be unique.');
									})
									->suffixIcon(fn ($get) => $get('duplicate_warning') ? 'heroicon-s-exclamation-triangle' : null)
									->suffixIconColor(fn ($get) => $get('duplicate_warning') ? 'danger' : null)
									->extraAttributes(function ($get) {
										return $get('duplicate_warning') ? ['class' => 'border-red-300'] : [];
									}),

								Select::make('type')
									->label('Field Type')
									->inlineLabel()
									->options([
										'text' => 'Simple Text - For short text',
										'textarea' => 'Text Area - For long text',
										'richtext' => 'Rich Text For formatted text',
									])
									->default('text')
									->required()
									->placeholder('can only hold up to 20 characters.')
									->live(),

								TextInput::make('value')
									->label('Value')
									->inlineLabel()
									->maxLength(20)
									->visible(fn ($get) => $get('type') === 'text'),

								Textarea::make('value')
									->label('Value')
									->inlineLabel()
									->rows(5)
									->visible(fn ($get) => $get('type') === 'textarea'),

								RichEditor::make('value')
									->label('Value')
									->inlineLabel()
									->toolbarButtons(['bulletList', 'orderedList'])
									->visible(fn ($get) => $get('type') === 'richtext'),

								TextInput::make('duplicate_warning')
									->hidden(),
							])
							->addActionLabel('Add Custom Field')
							// ->reorderable(false)
							->itemLabel(fn (array $state): ?string => $state['key'] ?? 'New Field')
							->defaultItems(0),
					]),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('company_id')
            ->columns([
                TextColumn::make('bank_acc_name')
					->label('Account Name')
					->formatStateUsing(function ($state) {
						if (!$state) {
							return null;
						}
						$words = explode(' ', $state);
						$truncated = array_slice($words, 0, 4);
						return implode(' ', $truncated) . (count($words) > 4 ? '...' : '');
					})
					->tooltip(fn ($state) => $state),
                TextColumn::make('bank_acc_no')
					->label('Account #')
					->tooltip(fn ($state) => $state),
                TextColumn::make('bank_name')
					->formatStateUsing(function ($state) {
						if (!$state) {
							return null;
						}
						$words = explode(' ', $state);
						$truncated = array_slice($words, 0, 4);
						return implode(' ', $truncated) . (count($words) > 4 ? '...' : '');
					})
					->tooltip(fn ($state) => $state),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->label('Add New Bank')
                    ->icon('heroicon-o-plus')
                    ->mutateFormDataUsing(function (array $data): array {
                        return $this->processCustomColumnsData($data);
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->mutateFormDataUsing(function (array $data): array {
                        return $this->processCustomColumnsData($data);
                    })
                    ->mutateRecordDataUsing(function (array $data): array {
                        return $this->mutateFormDataBeforeFill($data);
                    }),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    /**
     * Process custom columns data before saving
     *
     * @param array $data
     * @return array
     */
    protected function processCustomColumnsData(array $data): array
    {
        if (isset($data['custom_columns_data']) && is_array($data['custom_columns_data'])) {
            $customColumns = [];
            $deliveredColumns = CompanyBank::getDeliveredColumns();

            foreach ($data['custom_columns_data'] as $item) {
                if (!empty($item['key']) && isset($item['value']) && $item['value'] !== '') {
                    // Skip if key is in delivered columns
                    if (in_array($item['key'], $deliveredColumns)) {
                        continue;
                    }

                    // Skip if key already exists in current custom columns
                    if (array_key_exists($item['key'], $customColumns)) {
                        continue;
                    }

                    $customColumns[$item['key']] = [
                        'type' => $item['type'] ?? 'text',
                        'value' => $item['value']
                    ];
                }
            }

            $data['custom_columns'] = empty($customColumns) ? null : $customColumns;
        }

        // Remove the temporary field
        unset($data['custom_columns_data']);

        return $data;
    }

    /**
     * Mutate form data before filling (for edit)
     *
     * @param array $data
     * @return array
     */
    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Convert custom_columns to repeater format
        if (isset($data['custom_columns']) && is_array($data['custom_columns'])) {
            $customColumnsData = [];

            foreach ($data['custom_columns'] as $key => $value) {
                $customColumnsData[] = [
                    'key' => $key,
                    'type' => $value['type'] ?? 'text',
                    'value' => $value['value'] ?? '',
                ];
            }

            $data['custom_columns_data'] = $customColumnsData;
        }

        return $data;
    }
}
