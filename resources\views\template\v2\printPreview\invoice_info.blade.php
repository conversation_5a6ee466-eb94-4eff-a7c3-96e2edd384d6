@php
	// Extract configuration
	$invoiceTemplate = $payload['invoice']->invoicetemplate;
	$layoutConfig = $invoiceTemplate?->layout_config ?? [];
	$invoice = $payload['invoice'];

	// Invoice info configuration
	$invoiceInfoLayout = $layoutConfig['invoiceInfoLayout'] ?? 'stacked';
	$invoiceInfoWeight = $layoutConfig['invoiceInfoWeight'] ?? 'font-normal';
	$invoiceInfoIsBordered = $layoutConfig['invoiceHasBorder'] ?? false;
	$invoiceInfoAlignment = $layoutConfig['invoiceInfoAlignment'] ?? 'justify-start';
	$invoiceDateStyle = $layoutConfig['invoiceDateStyle'] ?? 'd-m-Y';
	$headingInvoiceTitleOnly = $layoutConfig['titleOnly'] ?? false;

	// Date formatting
	$invoiceDate = \Carbon\Carbon::parse($invoice->invoice_date);
	$dueDate = \Carbon\Carbon::parse($invoice->invoice_due);
@endphp

{{-- Invoice Info Section --}}
<div class="invoice-info-section">
	@if ($invoiceInfoLayout === 'stacked')
	<div class="flex {{ $invoiceInfoAlignment }}">
		<table class="text-compact border-separate">
			<tbody>
				@if (!$headingInvoiceTitleOnly)
					<tr>
						<td class="pe-3">Invoice #</td>
						<td>:</td>
						<td class="text-end ps-1 {{ $invoiceInfoWeight }}">{{ $invoice->invoice_no }}</td>
					</tr>
				@endif
				<tr>
					<td class="pe-3">Invoice Date</td>
					<td>:</td>
					<td class="text-end ps-1 {{ $invoiceInfoWeight }}">{{ $invoiceDate->format($invoiceDateStyle) }}</td>
				</tr>
				<tr>
					<td class="pe-3">Due Date</td>
					<td>:</td>
					<td class="text-end ps-1 {{ $invoiceInfoWeight }}">{{ $dueDate->format($invoiceDateStyle) }}</td>
				</tr>
			</tbody>
		</table>
	</div>
	@endif

	@if ($invoiceInfoLayout === 'grid')
		<div class="flex {{ $invoiceInfoAlignment }}">
			<table class="text-compact">
				<tbody>
					<tr>
						@if (!$headingInvoiceTitleOnly)
							<td class="px-2 py-1 {{ $invoiceInfoIsBordered ? 'border' : '' }} border-gray-300 text-center">Invoice No.</td>
						@endif
						<td class="px-2 py-1 {{ $invoiceInfoIsBordered ? 'border' : '' }} border-gray-300 text-center">Invoice Date</td>
						<td class="px-2 py-1 {{ $invoiceInfoIsBordered ? 'border' : '' }} border-gray-300 text-center">Due Date</td>
					</tr>
					<tr>
						@if (!$headingInvoiceTitleOnly)
							<td class="px-2 py-1 text-center {{ $invoiceInfoIsBordered ? 'border' : '' }} border-gray-300 {{ $invoiceInfoWeight }}">{{ $invoice->invoice_no }}</td>
						@endif
						<td class="px-2 py-1 text-center {{ $invoiceInfoIsBordered ? 'border' : '' }} border-gray-300 {{ $invoiceInfoWeight }}">{{ $invoiceDate->format($invoiceDateStyle) }}</td>
						<td class="px-2 py-1 text-center {{ $invoiceInfoIsBordered ? 'border' : '' }} border-gray-300 {{ $invoiceInfoWeight }}">{{ $dueDate->format($invoiceDateStyle) }}</td>
					</tr>
				</tbody>
			</table>
		</div>
	@endif

	@if ($invoiceInfoLayout === 'flex')
		<div class="flex {{ $invoiceInfoAlignment }} space-x-2">
			@if (!$headingInvoiceTitleOnly)
				<div class="{{ $invoiceInfoIsBordered ? 'border' : '' }} px-2 py-1">
					<span class="me-2">Invoice No.:</span>
					<span class="{{ $invoiceInfoWeight }}" style="white-space: nowrap">{{ $invoice->invoice_no }}</span>
				</div>
			@endif
			<div class="{{ $invoiceInfoIsBordered ? 'border' : '' }} px-2 py-1">
				<span class="me-2">Invoice Date:</span>
				<span class="{{ $invoiceInfoWeight }}" style="white-space: nowrap">{{ $invoiceDate->format($invoiceDateStyle) }}</span>
			</div>
			<div class="{{ $invoiceInfoIsBordered ? 'border' : '' }} px-2 py-1">
				<span class="me-2">Due Date:</span>
				<span class="{{ $invoiceInfoWeight }}" style="white-space: nowrap">{{ $dueDate->format($invoiceDateStyle) }}</span>
			</div>
		</div>
	@endif
</div>
