<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\MasterInvoiceDetailResource\Pages;
use App\Filament\Admin\Resources\MasterInvoiceDetailResource\RelationManagers;
use App\Models\BusinessType;
use App\Models\MasterInvoiceDetail;
use Filament\Forms;
use Filament\Forms\Components\{RichEditor, Select, TextInput};
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class MasterInvoiceDetailResource extends Resource
{
    protected static ?string $model = MasterInvoiceDetail::class;
	protected static bool $shouldRegisterNavigation = false;

	protected static ?string $navigationGroup = 'Masters Data';

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form

            ->schema([
                RichEditor::make('description')
					->extraInputAttributes(['style' => 'min-height: 10rem; max-height: 10vh; overflow-y: auto;'])
					->toolbarButtons(['bulletList', 'orderedlist'])
					->columnSpanFull(),
				TextInput::make('quantity')->numeric()->required()->live(onBlur:true)
					->extraInputAttributes(['class'=>'text-end']),
				TextInput::make('price')->numeric()->required()->live(onBlur:true)
					->extraInputAttributes(['class'=>'text-end']),
				Select::make('business_type')
						->inlineLabel()
						->label('Business Type')
						->required()
						->searchable()
						->options(
							BusinessType::get()
								->mapWithKeys(fn ($business) => [
									$business->id => $business->name
								])
								->toArray()
						),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('description')
					->formatStateUsing(function ($state) {
						preg_match('/<p>(.*?)<(?!\/p)(\w+)/', $state, $matches);

						if (isset($matches[1])) {
							$firstPart = $matches[1];
						} else {
							preg_match('/<p>(.*?)<\/p>/', $state, $matches);
							$firstPart = $matches[1] ?? '';
						}
						$cleanText = strip_tags($firstPart);
						return '<div class="line-clamp-3">' . $cleanText . '</div>';
					})
					->wrap()
					->html()
					->searchable()
					->tooltip(fn ($record) => $record->description),
				TextColumn::make('businessType.name')->searchable()
            ])
            ->filters([
				SelectFilter::make('business_type')
					// ->default(1)
					->label('Business Type')
					->relationship('businessType', 'name')
					->searchable()
					->preload(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMasterInvoiceDetails::route('/'),
            'create' => Pages\CreateMasterInvoiceDetail::route('/create'),
            'view' => Pages\ViewMasterInvoiceDetail::route('/{record}'),
            'edit' => Pages\EditMasterInvoiceDetail::route('/{record}/edit'),
        ];
    }
}
