<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\CompanyDepositSummary;
use App\Models\Order;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Spatie\Browsershot\Browsershot;

class OrderController extends Controller
{
	public function orderPerformancePayload(?string $startDate = null, ?string $endDate = null, array $agentIds = []): array
	{
		$query = Order::with(['agent', 'bank', 'company', 'currency', 'user']);

		if ($startDate && $endDate) {
			$query->whereBetween('order_date', [$startDate, $endDate]);
		}
		if (!empty($agentIds)) {
			$query->whereIn('agent_id', $agentIds);
		}

		$report = $query->get()->map(function ($order) {
			return [
				'order_no' => $order?->order_no,
				'order_create_by' => $order?->user?->name,
				'agent' => $order?->agent?->name,
				'company_id' => $order?->company?->name,
				'bank' => $order?->bank?->bank_name . ' - ' . $order?->bank?->bank_acc_no,
				'currency_id' => $order?->currency_id,
				'currency' => $order?->currency->symbol,
				'currencyName' => $order?->currency->name,
				'order_amount' => $order?->order_amount,
				'order_date' => $order?->order_date,
				'booking_fee' => $order?->booking_fee,
				'bank_rates' => $order?->rates,
				'sell_rates' => $order?->sell_rates,
				'charges' => $order?->charges,
				'total' => $order?->total,
				'totalPurchase' => $order?->rates * $order?->total,
				'totalSell' => $order?->sell_rates * $order?->total,
				'totalIdr' => ($order?->total + $order?->booking_fee) * $order?->rates,
				'totalProfit' => (($order?->sell_rates - $order?->rates) * $order?->total) + $order?->charges,
			];
		});

		//order summary
		$orderSummary = [
			'orderCount' => $report->count(),
			'orderTotal' => $report->sum('total'),
			'totalPurchase' => $report->sum('totalPurchase'),
			'totalSell' => $report->sum('totalSell'),
			'totalCharges' => $report->sum('charges'),
			'totalProfit' => $report->sum('totalProfit'),
		];
		//daily chart
		$dailyChart = $report
			// ->filter(fn ($item) => !in_array(\Carbon\Carbon::parse($item['order_date'])->dayOfWeek, [6, 0])) // 6: Saturday, 0: Sunday
			->groupBy('order_date')
			->map(fn($group) => [
				'orderCount' => collect($group)->count(),
				'orderTotal' => collect($group)->sum('total'),
				'totalPurchase' => collect($group)->sum('totalPurchase'),
				'totalSell' => collect($group)->sum('totalSell'),
				'totalCharges' => collect($group)->sum('charges'),
				'totalProfit' => collect($group)->sum('totalProfit'),
			])
			->sortKeys()
			->toArray();

		//order by currency
		$orderByCurrency = $report
			->groupBy('currencyName')
			->map(function (Collection $group) {
				return [
					'symbol'        => $group->first()['currency'] ?? '',
					'orderCount'    => $group->count(),
					'orderTotal'    => $group->sum('total'),
					'totalPurchase' => $group->sum('totalPurchase'),
					'totalSell'     => $group->sum('totalSell'),
					'totalCharges'     => $group->sum('charges'),
					'totalProfit'   => $group->sum('totalProfit'),
				];
			})
			->sortKeys()
			->toArray();
		$orderByAgent = $report
			->groupBy('agent')
			->map(function (Collection $group) {
				return [
					'agent'        => $group->first()['agent'] ?? '',
					'orderCount'    => $group->count(),
					'orderTotal'    => $group->sum('total'),
					'totalPurchase' => $group->sum('totalPurchase'),
					'totalSell'     => $group->sum('totalSell'),
					'totalCharges'     => $group->sum('charges'),
					'totalProfit'   => $group->sum('totalProfit'),
				];
			})
			->sortKeys()
			->toArray();
		return [
			'report' => $report->toArray(),
			'orderSummary' => $orderSummary,
			'daily_chart' => $dailyChart,
			'orderByCurrency' => $orderByCurrency,
			'orderByAgent' => $orderByAgent,
		];
	}

	public function printOrderTransactionPdf(Request $request)
	{
		$start = $request->query('start_date');
		$end = $request->query('end_date');

		// Ambil payload lengkap: report, chart, dan metadata
		$payload = $this->orderPerformancePayload($start, $end);

		$report = $payload['report'] ?? [];
		$orderSummary = $payload['orderSummary'] ?? [];
		$dailyChart = $payload['daily_chart'] ?? [];
		$orderByCurrency = $payload['orderByCurrency'] ?? [];

		$html = view('template.v2.order-transaction-pdf', [
			'report'        => $report,
			'orderSummary'    => $orderSummary,
			'dailyChart'    => $dailyChart,
			'orderByCurrency'    => $orderByCurrency,

			'dateStart'     => $start,
			'dateEnd'       => $end,
			'fontFamily'    => 'Inter',
			'fontSource'    => 'https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap',
			'paperSize'     => 'Legal',
			'marginTop'     => 5,
			'marginRight'   => 10,
			'marginBottom'  => 10,
			'marginLeft'    => 10,
		])->render();

		// Footer untuk browsershot
		$footerHtml = '<div style="font-family: Courier, monospace; font-size:10px; width:100%; text-align:center; margin:0 auto;">
			Generated by Rebrandz @ ' . now()->translatedFormat('d F Y') . ' &nbsp;&nbsp;|&nbsp;&nbsp; Page <span class="pageNumber"></span> of <span class="totalPages"></span>
		</div>';

		try {
			$pdf = Browsershot::html($html)
				->format('Legal')
				->margins(10, 10, 10, 10)
				->noSandbox()
				->scale(0.85)
				->showBackground()
				->waitUntilNetworkIdle()
				->setOption('printBackground', true)
				->setOption('preferCSSPageSize', false)
				->setOption('displayHeaderFooter', true)
				->setOption('headerTemplate', '<div></div>')
				->setOption('footerTemplate', $footerHtml)
				->pdf();

			return response($pdf)
				->header('Content-Type', 'application/pdf')
				->header('Content-Disposition', 'inline; filename="Invoice_Performance_Report.pdf"');
		} catch (\Exception $e) {
			return $html; // fallback debug
		}
	}

	public function printOrderRevenuePdf(Request $request)
	{
		$start = $request->query('start_date');
		$end = $request->query('end_date');
		$agents = $request->query('agent_id', []);
		$agents = is_array($agents) ? $agents : [$agents];

		// Ambil payload lengkap: report, chart, dan metadata
		$payload = $this->orderPerformancePayload($start, $end, $agents);

		$report = $payload['report'] ?? [];
		$orderSummary = $payload['orderSummary'] ?? [];
		$dailyChart = $payload['daily_chart'] ?? [];
		$orderByCurrency = $payload['orderByCurrency'] ?? [];
		$orderByAgent = $payload['orderByAgent'] ?? [];

		$html = view('template.v2.order-performance-pdf', [
			'report'        => $report,
			'orderSummary'    => $orderSummary,
			'dailyChart'    => $dailyChart,
			'orderByAgent'    => $orderByAgent,

			'dateStart'     => $start,
			'dateEnd'       => $end,
			'fontFamily'    => 'Inter',
			'fontSource'    => 'https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap',
			'paperSize'     => 'Legal',
			'marginTop'     => 5,
			'marginRight'   => 10,
			'marginBottom'  => 10,
			'marginLeft'    => 10,
		])->render();

		// Footer untuk browsershot
		$footerHtml = '<div style="font-family: Courier, monospace; font-size:10px; width:100%; text-align:center; margin:0 auto;">
			Generated by Rebrandz @ ' . now()->translatedFormat('d F Y') . ' &nbsp;&nbsp;|&nbsp;&nbsp; Page <span class="pageNumber"></span> of <span class="totalPages"></span>
		</div>';

		try {
			$pdf = Browsershot::html($html)
				->format('Legal')
				->margins(10, 10, 10, 10)
				->noSandbox()
				->scale(0.85)
				->showBackground()
				->waitUntilNetworkIdle()
				->setOption('printBackground', true)
				->setOption('preferCSSPageSize', false)
				->setOption('displayHeaderFooter', true)
				->setOption('headerTemplate', '<div></div>')
				->setOption('footerTemplate', $footerHtml)
				->pdf();

			return response($pdf)
				->header('Content-Type', 'application/pdf')
				->header('Content-Disposition', 'inline; filename="Invoice_Performance_Report.pdf"');
		} catch (\Exception $e) {
			return $html; // fallback debug
		}
	}

	public function printOrderRevenuePreview(Request $request)
	{
		$start = $request->query('start_date');
		$end = $request->query('end_date');
		$agents = $request->query('agent_id', []);
		$agents = is_array($agents) ? $agents : [$agents];

		// Ambil payload lengkap: report, chart, dan metadata
		$payload = $this->orderPerformancePayload($start, $end, $agents);

		$report = $payload['report'] ?? [];
		$orderSummary = $payload['orderSummary'] ?? [];
		$dailyChart = $payload['daily_chart'] ?? [];
		$orderByAgent = $payload['orderByAgent'] ?? [];

		// Tampilkan langsung sebagai HTML view biasa
		return view('template.v2.order-performance-pdf', [
			'report'        => $report,
			'orderSummary'  => $orderSummary,
			'dailyChart'    => $dailyChart,
			'orderByAgent'  => $orderByAgent,

			'dateStart'     => $start,
			'dateEnd'       => $end,
			'fontFamily'    => 'Inter',
			'fontSource'    => 'https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap',
			'paperSize'     => 'Legal',
			'marginTop'     => 5,
			'marginRight'   => 10,
			'marginBottom'  => 10,
			'marginLeft'    => 10,
		]);
	}

	public function getDashboardStatPayload()
	{
		$now = now('Asia/Jakarta');

		$calcRevenue = fn($query) => $query
			->selectRaw('SUM(((sell_rates - rates) * total) + charges) AS revenue')
			->value('revenue') ?? 0;

		$baseQuery = fn() => Order::query();

		// Pendefinisian waktu
		$thisYear      = $now->year;
		$lastYear      = $now->copy()->subYear()->year;

		$thisMonth     = $now->month;
		$lastMonthDate = $now->copy()->subMonth();
		$lastMonth     = $lastMonthDate->month;
		$lastMonthYear = $lastMonthDate->year;

		$thisWeekStart = $now->copy()->startOfWeek();
		$thisWeekEnd   = $now->copy()->endOfWeek();

		$lastWeekStart = $now->copy()->subWeek()->startOfWeek();
		$lastWeekEnd   = $now->copy()->subWeek()->endOfWeek();

		$today         = $now->toDateString();
		$yesterday     = $now->copy()->subDay()->toDateString();

		// Perhitungan Revenue
		$revenueThisYear   = $calcRevenue(($baseQuery())->whereYear('order_date', $thisYear));
		$revenueLastYear   = $calcRevenue(($baseQuery())->whereYear('order_date', $lastYear));

		$revenueThisMonth  = $calcRevenue(($baseQuery())
			->whereYear('order_date', $thisYear)
			->whereMonth('order_date', $thisMonth));

		$revenueLastMonth  = $calcRevenue(($baseQuery())
			->whereYear('order_date', $lastMonthYear)
			->whereMonth('order_date', $lastMonth));

		$revenueThisWeek   = $calcRevenue(($baseQuery())
			->whereBetween('order_date', [$thisWeekStart, $thisWeekEnd]));

		$revenueLastWeek   = $calcRevenue(($baseQuery())
			->whereBetween('order_date', [$lastWeekStart, $lastWeekEnd]));

		$revenueToday      = $calcRevenue(($baseQuery())->whereDate('order_date', $today));
		$revenueYesterday  = $calcRevenue(($baseQuery())->whereDate('order_date', $yesterday));

		// Perhitungan Persentase Perbandingan
		$percent = fn($now, $past) => $now !== 0 ? round((($now - $past) / $now) * 100, 2) : 0;

		return [
			'revenue' => [
				'year'  => $revenueThisYear,
				'month' => $revenueThisMonth,
				'week'  => $revenueThisWeek,
				'day'   => $revenueToday,
			],
			'difference' => [
				'year'  => $percent($revenueThisYear, $revenueLastYear),
				'month' => $percent($revenueThisMonth, $revenueLastMonth),
				'week'  => $percent($revenueThisWeek, $revenueLastWeek),
				'day'   => $percent($revenueToday, $revenueYesterday),
			],
			'datenumbering' => [
				'year'  => substr((string) $now->year, -2),
				'month' => str_pad((string) $now->month, 2, '0', STR_PAD_LEFT),
				'week'  => str_pad((string) $now->weekOfYear, 2, '0', STR_PAD_LEFT),
				'day'   => str_pad((string) $now->day, 2, '0', STR_PAD_LEFT),
			],
		];
	}

	public function getStackedCashFlowPayload()
	{
		$now = now('Asia/Jakarta');
			$thisWeekStart = $now->copy()->startOfWeek();
			$thisWeekEnd = $now->copy()->endOfWeek();

			$lastWeekStart = $now->copy()->subWeek()->startOfWeek();
			$lastWeekEnd = $now->copy()->subWeek()->endOfWeek();

			$thisMonth = $now->month;
			$thisYear = $now->year;

			$grouped = Order::withoutGlobalScope('order')
				->select([
					'currency_id',
					DB::raw('COUNT(*) as orderCount'),
					DB::raw('SUM(total) as orderTotal'),
					DB::raw('SUM(rates * total) as totalPurchase'),
					DB::raw('SUM(sell_rates * total) as totalSell'),
					DB::raw('SUM(charges) as totalCharges'),
					DB::raw('SUM(((sell_rates - rates) * total) + charges) as totalProfit'),
				])
				->with('currency:id,name,symbol')
				->groupBy('currency_id')
				->get()
				->mapWithKeys(function ($item) {
					$name = $item->currency->name ?? 'Unknown';
					return [
						$name => [
							'symbol' => $item->currency->symbol ?? '',
							'orderCount' => $item->orderCount,
							'orderTotal' => $item->orderTotal,
							'totalPurchase' => $item->totalPurchase,
							'totalSell' => $item->totalSell,
							'totalCharges' => $item->totalCharges,
							'totalProfit' => $item->totalProfit,
						]
					];
				});


			$sumPurchase = fn($query) => $query->sum(DB::raw('rates * total'));

			$lastWeekPurchase = $sumPurchase(
				Order::whereBetween('order_date', [$lastWeekStart, $lastWeekEnd])
			);

			$thisWeekPurchase = $sumPurchase(
				Order::whereBetween('order_date', [$thisWeekStart, $thisWeekEnd])
			);

			$thisMonthPurchase = $sumPurchase(
				Order::whereYear('order_date', $thisYear)
					->whereMonth('order_date', $thisMonth)
			);

			$mostTradedKey = $grouped->sortByDesc('totalSell')->keys()->first();
			$mostTraded = $grouped[$mostTradedKey] ?? [];

			$mostTransactionedKey = $grouped->sortByDesc('orderCount')->keys()->first();
			$mostTransactioned = $grouped[$mostTransactionedKey] ?? [];
			$totalBalance = CompanyDepositSummary::sum('balance') ?? 0;
			$cashFlowData = DB::table('company_depositos')
					->whereNull('deleted_at')
					->selectRaw('
                    SUM(CASE WHEN trx_type = "in" THEN amount ELSE 0 END) as total_in,
                    SUM(CASE WHEN trx_type = "out" THEN amount ELSE 0 END) as total_out,
                    SUM(CASE WHEN trx_type = "order" THEN amount ELSE 0 END) as total_order
                ')
				->first();

			$cashFlowTotal = ($cashFlowData->total_in ?? 0) - ($cashFlowData->total_out ?? 0) - ($cashFlowData->total_order ?? 0);
			$lowBalanceThreshold = 1_000_000;
			$lowBalanceBanks = CompanyDepositSummary::where('balance', '<', $lowBalanceThreshold)->count();

			return [
				'leadingTradedCurrency' => [
					'name' => $mostTradedKey,
					'symbol' => $mostTraded['symbol'] ?? '',
					'orderCount' => $mostTraded['orderCount'] ?? 0,
					'totalSell' => $mostTraded['totalSell'] ?? 0,
				],
				'leadingTransactionedCurrency' => [
					'name' => $mostTransactionedKey,
					'symbol' => $mostTransactioned['symbol'] ?? '',
					'orderCount' => $mostTransactioned['orderCount'] ?? 0,
					'totalSell' => $mostTransactioned['totalSell'] ?? 0,
				],
				'transactionInsight' => [
					'lastWeekPurchase' => $lastWeekPurchase,
					'thisWeekPurchase' => $thisWeekPurchase,
					'thisMonthPurchase' => $thisMonthPurchase,
				],
				'totalBalance' => number_format($totalBalance, 2, ',', '.'),
				'cashFlowTotal' => number_format($cashFlowTotal, 2, ',', '.'),
				'lowBalanceBanks' => $lowBalanceBanks,
			];
	}

	public function getTrendsPayload()
	{
		$now = Carbon::now('Asia/Jakarta');

		// Total Balance
		$totalBalance = CompanyDepositSummary::sum('balance') ?? 0;

		// Cash Flow Total
		$cashFlowData = DB::table('company_depositos')
			->whereNull('deleted_at')
			->selectRaw('
            SUM(CASE WHEN trx_type = "in" THEN amount ELSE 0 END) as total_in,
            SUM(CASE WHEN trx_type = "out" THEN amount ELSE 0 END) as total_out,
            SUM(CASE WHEN trx_type = "order" THEN amount ELSE 0 END) as total_order
        ')
			->first();

		$cashFlowTotal = ($cashFlowData->total_in ?? 0) - ($cashFlowData->total_out ?? 0) - ($cashFlowData->total_order ?? 0);

		// Low Balance Banks
		$lowBalanceThreshold = 1000000;
		$lowBalanceBanks = CompanyDepositSummary::where('balance', '<', $lowBalanceThreshold)->count();

		return [
			'totalBalance' => number_format($totalBalance, 2, ',', '.'),
			'cashFlowTotal' => number_format($cashFlowTotal, 2, ',', '.'),
			'lowBalanceBanks' => $lowBalanceBanks,
		];
	}

	public function getChartPayload()
	{
		$weeks = [];
        $orderCounts = [];
        $orderValues = [];

        for ($i = 3; $i >= 0; $i--) {
            $startOfWeek = Carbon::now()->subWeeks($i)->startOfWeek();
            $endOfWeek = Carbon::now()->subWeeks($i)->endOfWeek();

            // Week label
            $weeks[] = $startOfWeek->format('M d') . ' - ' . $endOfWeek->format('M d');

            // Order count for this week
            $weeklyCount = Order::whereBetween('order_date', [$startOfWeek, $endOfWeek])->count();
            $orderCounts[] = $weeklyCount;

            // Order value for this week (total * rates converted to IDR)
            $weeklyValue = Order::whereBetween('order_date', [$startOfWeek, $endOfWeek])
                ->selectRaw('SUM(total * rates) as total_value')
                ->value('total_value') ?? 0;
            $orderValues[] = round($weeklyValue / 1000000, 2); // Convert to millions for readability
        }

        return [
            'datasets' => [
                [
                    'label' => 'Order Count',
                    'data' => $orderCounts,
                    'type' => 'line',
                    'borderColor' => 'rgb(59, 130, 246)', // Blue
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                    'yAxisID' => 'y',
                    'tension' => 0.3,
                ],
                [
                    'label' => 'Order Value (M IDR)',
                    'data' => $orderValues,
                    'type' => 'bar',
                    'backgroundColor' => 'rgba(16, 185, 129, 0.6)', // Green
                    'borderColor' => 'rgb(16, 185, 129)',
                    'yAxisID' => 'y1',
                ],
            ],
            'labels' => $weeks,
        ];
	}

	public function printDashboardPdf()
	{
		$statPayload = $this->getDashboardStatPayload();
		$cashFlowPayload = $this->getStackedCashFlowPayload();
		$trendsPayload = $this->getTrendsPayload();
		$chartPayload = $this->getChartPayload();

		// Log::info($statPayload);
		// Log::info($cashFlowPayload);
		// Log::info($trendsPayload);
		// Log::info($chartPayload);
		// dd('stop');


		$html = view('template.v2.dashboard-print-pdf', [
			// 'report'        => $report,
			// 'orderSummary'    => $orderSummary,
			// 'dailyChart'    => $dailyChart,
			// 'orderByAgent'    => $orderByAgent,

			// 'dateStart'     => $start,
			// 'dateEnd'       => $end,
			'fontFamily'    => 'Inter',
			'fontSource'    => 'https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap',
			'paperSize'     => 'Legal',
			'marginTop'     => 5,
			'marginRight'   => 10,
			'marginBottom'  => 10,
			'marginLeft'    => 10,
		])->render();

		// Footer untuk browsershot
		$footerHtml = '<div style="font-family: Courier, monospace; font-size:10px; width:100%; text-align:center; margin:0 auto;">
			Generated by Rebrandz @ ' . now()->translatedFormat('d F Y') . ' &nbsp;&nbsp;|&nbsp;&nbsp; Page <span class="pageNumber"></span> of <span class="totalPages"></span>
		</div>';

		try {
			$pdf = Browsershot::html($html)
				->format('Legal')
				->margins(10, 10, 10, 10)
				->noSandbox()
				->scale(0.85)
				->showBackground()
				->waitUntilNetworkIdle()
				->setOption('printBackground', true)
				->setOption('preferCSSPageSize', false)
				->setOption('displayHeaderFooter', true)
				->setOption('headerTemplate', '<div></div>')
				->setOption('footerTemplate', $footerHtml)
				->pdf();

			return response($pdf)
				->header('Content-Type', 'application/pdf')
				->header('Content-Disposition', 'inline; filename="Invoice_Performance_Report.pdf"');
		} catch (\Exception $e) {
			return $html; // fallback debug
		}
	}
}
