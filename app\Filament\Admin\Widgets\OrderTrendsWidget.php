<?php

namespace App\Filament\Admin\Widgets;

use App\Models\Order;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class OrderTrendsWidget extends ChartWidget
{
    protected function getData(): array
    {
        // Get last 6 weeks of data
        $weeks = [];
        $orderCounts = [];
        $orderValues = [];

        for ($i = 3; $i >= 0; $i--) {
            $startOfWeek = Carbon::now()->subWeeks($i)->startOfWeek();
            $endOfWeek = Carbon::now()->subWeeks($i)->endOfWeek();

            // Week label
            $weeks[] = $startOfWeek->format('M d') . ' - ' . $endOfWeek->format('M d');

            // Order count for this week
            $weeklyCount = Order::whereBetween('order_date', [$startOfWeek, $endOfWeek])->count();
            $orderCounts[] = $weeklyCount;

            // Order value for this week (total * rates converted to IDR)
            $weeklyValue = Order::whereBetween('order_date', [$startOfWeek, $endOfWeek])
                ->selectRaw('SUM(total * rates) as total_value')
                ->value('total_value') ?? 0;
            $orderValues[] = round($weeklyValue / 1000000, 2); // Convert to millions for readability
        }

        return [
            'datasets' => [
                [
                    'label' => 'Order Count',
                    'data' => $orderCounts,
                    'type' => 'line',
                    'borderColor' => 'rgb(59, 130, 246)', // Blue
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                    'yAxisID' => 'y',
                    'tension' => 0.3,
                ],
                [
                    'label' => 'Order Value (M IDR)',
                    'data' => $orderValues,
                    'type' => 'bar',
                    'backgroundColor' => 'rgba(16, 185, 129, 0.6)', // Green
                    'borderColor' => 'rgb(16, 185, 129)',
                    'yAxisID' => 'y1',
                ],
            ],
            'labels' => $weeks,
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }

    protected function getOptions(): array
    {
        return [
            'responsive' => true,
            'interaction' => [
                'mode' => 'index',
                'intersect' => false,
            ],
            'scales' => [
                'y' => [
                    'type' => 'linear',
                    'display' => true,
                    'position' => 'left',
					'grid' => [
						'color' => 'rgba(0, 0, 0, 0.1)',
					],
                    'title' => [
                        'display' => true,
                        'text' => 'Order Count',
                    ],
                ],
                'y1' => [
                    'type' => 'linear',
                    'display' => true,
                    'position' => 'right',
					'grid' => [
						'color' => 'rgba(0, 0, 0, 0.1)',
					],
                    'title' => [
                        'display' => true,
                        'text' => 'Value (Million IDR)',
                    ],
                    'grid' => [
                        'drawOnChartArea' => false,
                    ],
                ],
            ],
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'top',
                ],
                'tooltip' => [
                    'mode' => 'index',
                    'intersect' => false,
                ],
            ],
        ];
    }
    // protected static ?string $heading = 'Weekly Trends';
    protected static ?string $pollingInterval = '120s';
    protected int | string | array $columnSpan = 3;

	public static function canView(): bool
    {
        return Auth::user()->hasAnyRole(['admin', 'Super Admin']);
    }
	public static function canAccess(): bool
    {
        return Auth::user()->hasAnyRole(['admin', 'Super Admin']);
    }
}
