<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\CompanyDeposito;
use Illuminate\Http\Request;
use Spatie\Browsershot\Browsershot;

class CompanyFinancialController extends Controller
{
	public function financialPerformancePayload(?string $startDate = null, ?string $endDate = null): array
	{
		$query = CompanyDeposito::with(['company', 'bank', 'order']);

		if ($startDate && $endDate) {
			$query->whereBetween('trx_date', [$startDate, $endDate]);
		}

		$report = $query->get()->map(function ($item) {
			$isOrder = $item->trx_type === 'order';

			return [
				'id'            => $item?->id,
				'company_id'    => $item?->company_id,
				'company'       => $item?->company?->name ?? 'Unknown Company',
				'bank_id'       => $item?->bank_id,
				'bank'          => $item?->bank?->bank_name ?? 'Unknown Bank',
				'date'          => $item?->trx_date,
				'type'          => $item?->trx_type,
				'amount'        => $isOrder ? null : $item?->amount,
				'order_amount'  => $isOrder ? $item?->amount : null,
				'description'   => $item?->description,
			];
		});

		// Grouping & balance logic
		$grouped = $report->groupBy('company_id')->map(function ($companyGroup) {
			/** @var \Illuminate\Support\Collection $companyGroup */
			return $companyGroup->groupBy('bank_id')->map(function ($bankGroup) {
				$totalIn = $bankGroup->where('type', 'in')->sum('amount');
				$totalOut = $bankGroup->where('type', 'out')->sum('amount');
				$totalOrder = $bankGroup->where('type', 'order')->sum('order_amount');

				$balance = $totalIn - ($totalOut + $totalOrder);

				return [
					'balance' => $balance,
					'transactions' => $bankGroup->values()->all(),
				];
			});
		});

		return [
			'report' => $grouped->toArray(),
		];
	}

	public function printFinancialPerformancePdf(Request $request)
	{
		$start = $request->query('start_date');
		$end = $request->query('end_date');

		// Ambil payload lengkap: report, chart, dan metadata
		$payload = $this->financialPerformancePayload($start, $end);

		$report = $payload['report'];

		$html = view('template.v2.financial-report-pdf', [
			'report'        => $report,
			'dateStart'     => $start ?? null,
			'dateEnd'       => $end ?? null,
			'fontFamily'    => 'Inter',
			'fontSource'    => 'https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap',
			'paperSize'     => 'Legal',
			'marginTop'     => 5,
			'marginRight'   => 10,
			'marginBottom'  => 10,
			'marginLeft'    => 10,
		])->render();

		// Footer untuk browsershot
		$footerHtml = '<div style="font-family: Courier, monospace; font-size:10px; width:100%; text-align:center; margin:0 auto;">
			Generated by Rebrandz @ ' . now()->translatedFormat('d F Y') . ' &nbsp;&nbsp;|&nbsp;&nbsp; Page <span class="pageNumber"></span> of <span class="totalPages"></span>
		</div>';

		try {
			$pdf = Browsershot::html($html)
				->format('Legal')
				->margins(10, 10, 10, 10)
				->noSandbox()
				->showBackground()
				->waitUntilNetworkIdle()
				->setOption('printBackground', true)
				->setOption('preferCSSPageSize', false)
				->setOption('displayHeaderFooter', true)
				->setOption('headerTemplate', '<div></div>')
				->setOption('footerTemplate', $footerHtml)
				->pdf();

			return response($pdf)
				->header('Content-Type', 'application/pdf')
				->header('Content-Disposition', 'inline; filename="Companies_Financial_Performance.pdf"');
		} catch (\Exception $e) {
			return $html; // fallback debug
		}
	}
}
