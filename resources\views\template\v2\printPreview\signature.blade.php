@php
	// Extract configuration
	$invoiceTemplate = $payload['invoice']->invoicetemplate;
	$layoutConfig = $invoiceTemplate?->layout_config ?? [];
	$company = $payload['invoice']->company;

	// Signature configuration
	$signatureStyle = $layoutConfig['signatureStyle'] ?? 'style1';
	$signatureHeight = $layoutConfig['signatureHeight'] ?? 4;
	$signatureFontWeight = $layoutConfig['signatureFontWeight'] ?? 'font-normal';
	$signatureHorizontalAlign = $layoutConfig['signatureHorizontalAlign'] ?? 'text-center';
	$signatureNameUppercase = $layoutConfig['signatureNameUppercase'] ?? 'none';

	// Company data
	$companySignature = $company->signature;
	$companySignatureName = $company->signature_name;
	$companyName = $company->name;
@endphp

<div class="flex flex-col {{ $signatureHorizontalAlign }}">
	{{-- style 1 --}}
	@if ($signatureStyle === 'style1')
		@if ($companySignature)
			<img src="{{ asset('storage/'. $companySignature) }}" style="max-height:{{ $signatureHeight }}rem;" alt="Signature" class="object-contain" />
		@endif
		<span class="text-sm {{ $signatureFontWeight }}" style="border-bottom: 1px solid #555; text-transform: {{ $signatureNameUppercase }}">{{ $companySignatureName }}</span>
		<span class="text-xs text-gray-500">{{ $companyName }}</span>
	@endif

	{{-- style 2 --}}
	@if ($signatureStyle === 'style2')
		<div class="flex items-center space-x-4">
			<div class="flex flex-col {{ $signatureHorizontalAlign }} space-y-1 pe-3 border-e">
				<span>Signed by</span>
				<span class="{{ $signatureFontWeight }}" style="text-transform: {{ $signatureNameUppercase }}">{{ $companySignatureName }}</span>
				<span>{{ $companyName }}</span>
			</div>
			@if ($companySignature)
				<img src="{{ asset('storage/'. $companySignature) }}" alt="Signature" class="object-contain" style="max-height:{{ $signatureHeight }}rem;"/>
			@endif
		</div>
	@endif

	{{-- style 3 --}}
	@if ($signatureStyle === 'style3')
		<div class="flex items-end space-x-4">
			@if ($companySignature)
				<img src="{{ asset('storage/'. $companySignature) }}" alt="Signature" class="object-contain pe-3 border-e" style="max-height:{{ $signatureHeight }}rem;"/>
			@endif
			<div class="flex flex-col {{ $signatureHorizontalAlign }} space-y-1">
				<span class="{{ $signatureFontWeight }}" style="text-transform: {{ $signatureNameUppercase }}">{{ $companySignatureName }}</span>
				<span>{{ $companyName }}</span>
			</div>
		</div>
	@endif
</div>
