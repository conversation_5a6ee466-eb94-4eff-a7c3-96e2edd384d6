<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CompanyDeposito extends Model
{
	use SoftDeletes;

	protected $table = 'company_depositos';

	protected $fillable = [
		'company_id',
		'order_id',
		'bank_id',
		'description',
		'trx_date',
		'trx_type',
		'amount',
	];



	public function company()
	{
		return $this->belongsTo(Company::class, 'company_id', 'id')->where('type', 2);
	}

	public function order()
	{
		return $this->belongsTo(Order::class, 'order_id', 'id');
	}

	public function bank()
	{
		return $this->belongsTo(CompanyBank::class, 'bank_id', 'id');
	}

	/**
	 * Check if this deposito can be deleted manually
	 */
	public function canBeDeleted(): bool
	{
		return is_null($this->order_id);
	}

	/**
	 * Get deletion prevention message
	 */
	public function getDeletionPreventionMessage(): string
	{
		if ($this->canBeDeleted()) {
			return '';
		}

		$orderNo = $this->order ? $this->order->order_no : $this->order_id;
		return "Deposito ini terkait dengan Order #{$orderNo}. Untuk menghapus deposito, hapus atau edit Order terkait.";
	}
}
