<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Invoice Performance Report</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="{{ $fontSource }}" rel="stylesheet">
    @vite('resources/css/app.css')

    <style>
        @page {
            size: {{ $paperSize }};
            margin: {{ $marginTop }}mm {{ $marginRight }}mm {{ $marginBottom }}mm {{ $marginLeft }}mm;
        }

		@media print {
			.page-break {
				page-break-before: always;
			}
		}


        body {
            font-family: '{{ $fontFamily }}', sans-serif;
            font-size: 12px;
            color: #111827;
            line-height: 1.4;
        }

        th, td {
            border: 1px solid #d1d5db;
            padding: 6px 8px;
            text-align: left;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        thead {
            background-color: #f0f0f0;
            font-weight: 600;
        }

        .text-right {
            text-align: right;
        }

        .text-center {
            text-align: center;
        }

        .mb-4 {
            margin-bottom: 1rem;
        }

        .uppercase {
            text-transform: uppercase;
        }

        .font-bold {
            font-weight: bold;
        }
		@page {
			size: 355.6mm 215.9mm;
		}
    </style>
</head>
<body class=" text-xs">
	<div class="grid grid-cols-2 justify-between mb-4 items-center">
		<div class="col-span-1">
			<h2 class="text-xl font-bold uppercase">Bank Statement Report</h2>
			<div>
				<span class="me-1">Start Date: </span>
				<span class="me-3 font-semibold">
					{{ $dateStart ? \Carbon\Carbon::parse($dateStart)->translatedFormat('d F Y') : 'Unfiltered' }}
				</span>

				<span class="me-1">to: </span>
				<span class="font-semibold">
					{{ $dateEnd ? \Carbon\Carbon::parse($dateEnd)->translatedFormat('d F Y') : 'Unfiltered' }}
				</span>
			</div>
		</div>
		<div class="col-span-1 text-end">
			{{-- <span>Report Date: {{ \Carbon\Carbon::today()->format('d F Y') }}</span> --}}
		</div>
	</div>
	<table class="w-full text-sm">
		<thead class="bg-gray-100 text-left">
			<tr>
				<th class="p-2" width="10%">Company</th>
				<th class="p-2" width="10%">Bank</th>
				<th class="p-2" width="10%">Date</th>
				<th class="p-2" width="5%">Type</th>
				<th class="p-2 text-right" width="15%">Amount</th>
				<th class="p-2 text-right" width="15%">Order Amount</th>
				<th class="p-2" width="35%">Description</th>
			</tr>
		</thead>
		<tbody>
			@foreach ($report as $companyId => $banks)
				@php
					$firstTrx = collect($banks)->first()['transactions'][0] ?? null;
        			$companyName = $firstTrx['company'] ?? 'Unknown Company';
				@endphp
				<tr class="bg-gray-200 font-bold p-2">
					<td colspan="7">
						{{ $companyName }}
					</td>
				</tr>

				@foreach ($banks as $bankId => $bankData)
					@php
						$bankName = $bankData['transactions'][0]['bank'] ?? 'Unknown Bank';
            			$balance = $bankData['balance'];
					@endphp
					<tr class="bg-gray-100 p-2">
						<td class=""></td>
						<td class="font-semibold" colspan="4">
							{{ $bankName }}
						</td>
						<td class="" colspan="2">
							<div class="flex justify-between">
								<span>
								</span>
								<span class="space-x-2">
									<span>Balance: </span><span class="font-semibold">Rp {{ number_format($balance ?? 0, 2, ',', '.') }}</span>
								</span>
							</div>
						</td>
					</tr>

					@foreach ($bankData['transactions'] as $trx)
						<tr class="p-2">
							<td colspan="2"></td>
							<td class="text-center">{{ \Carbon\Carbon::parse($trx['date'])->format('d M Y') }}</td>
							<td class="uppercase text-center">
								{{ $trx['type'] }}
							</td>
							<td class="text-right">{{ number_format($trx['amount'] ?? 0, 2,',','.') }}</td>
							<td class="text-right">{{ number_format($trx['order_amount'] ?? 0, 2,',','.') }}</td>
							<td>{{ $trx['description'] }}</td>
						</tr>
					@endforeach
				@endforeach
			@endforeach
		</tbody>
	</table>
</body>
</html>
