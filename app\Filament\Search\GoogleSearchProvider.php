<?php

namespace App\Filament\Search;

use App\Models\GoogleSearchLog;
use Filament\GlobalSearch\Contracts\GlobalSearchProvider;
use Filament\GlobalSearch\GlobalSearchResult;
use Filament\GlobalSearch\GlobalSearchResults;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use App\Filament\Search\GoogleSearchResult;

class GoogleSearchProvider implements GlobalSearchProvider
{
    public function getResults(string $query): ?GlobalSearchResults
    {
        if (! str_starts_with($query, 'g:')) {
            return null;
        }

        $cleanQuery = trim(substr($query, 2));

        $countToday = GoogleSearchLog::whereDate('created_at', today())->count();

        if ($countToday >= 100) {
            Log::info('GoogleSearchProvider: Kuota harian habis untuk query: ' . $cleanQuery);
            return null;
        }

        $response = Http::get('https://www.googleapis.com/customsearch/v1', [
            'key' => config('services.google.api_key'),
            'cx'  => config('services.google.cse_id'),
            'q'   => $cleanQuery,
        ]);

        $items = $response->json('items') ?? [];

        // ✅ Logging ke Laravel log
        Log::info('GoogleSearchProvider Query', [
            'query' => $cleanQuery,
            'total_results' => count($items),
            'sample_results' => collect($items)->take(3)->map(fn($i) => [
                'title' => $i['title'] ?? '',
                'link' => $i['link'] ?? '',
            ]),
        ]);

        // ✅ Simpan log pencarian
        GoogleSearchLog::create([
            'query' => $cleanQuery,
            'created_at' => now(),
        ]);
		$results = collect($items)->map(function ($item) {
			$result = new \Filament\GlobalSearch\GlobalSearchResult(
				$item['title'],
				$item['link'],
				[$item['snippet'] ?? '']
			);

			$result->group = 'Google Search'; // 👈 manual inject group

			return $result;
		})->all();
		return new \Filament\GlobalSearch\GlobalSearchResults($results);
    }
}
