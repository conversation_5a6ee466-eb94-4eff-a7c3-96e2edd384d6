@props(['invoice'])

@php
	// Build chain data
	$chainData = [];
	$current = $invoice;

	// Go to root (main invoice)
	while ($current->parent_invoice_id) {
		$current = $current->parentInvoice;
	}

	// Build chain from root
	$chainData[] = $current;
	while ($current->childInvoice()->exists()) {
		$current = $current->childInvoice()->first();
		$chainData[] = $current;
	}

	$currentInvoiceId = $invoice->id;
	$totalChain = count($chainData);
@endphp

<div class="bg-gray-50 dark:bg-transparent p-4 rounded-lg border dark:border-0">
	<div class="flex items-center justify-between mb-3">
		<h4 class="text-sm font-medium text-gray-900"> </h4>
		<span class="text-xs text-gray-500">{{ $totalChain }} {{ $totalChain > 1 ? 'invoices' : 'invoice' }} chainings</span>
	</div>

	<div class="flex items-center space-x-2 overflow-x-auto pb-2">
		@foreach($chainData as $index => $chainInvoice)
			{{-- Company Box --}}
			<div class="flex-shrink-0 relative w-48">
				<div class="flex flex-col items-center w-full">
					{{-- Company Badge --}}
					@php
						$isActive = $chainInvoice->id === $currentInvoiceId;
						$statusColor = match ($chainInvoice->status) {
							'Draft', 'Issued' => 'warning',
							'Closed' => 'success',
							default => 'danger',
						};
					@endphp
					<div
						class="px-2 py-3 rounded-lg border-2 text-center w-48 flex flex-col justify-between relative transition-all duration-200
							{{ $isActive
								? 'bg-white dark:bg-transparent border-orange-600 text-orange-900 dark:text-orange-100 shadow-md'
								: 'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-700 text-gray-700 dark:text-gray-200 hover:border-gray-400 dark:hover:border-gray-500'
							}}"
					>
						<div class="flex-1 flex items-start justify-start mb-3">
							<div class="flex justify-start items-center gap-2">
								<div class="w-8 h-8 rounded-full border-2 flex items-center justify-center text-xs font-bold shrink-0">
									{{ $index === 0 ? 'M' : 'L' . ($index + 1) }}
								</div>
								<div class="text-sm font-semibold leading-tight text-start overflow-hidden"
									title="{{ $chainInvoice->company->name ?? 'Unknown' }}">
									<div style="display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden; word-break: break-word; line-height: 1.2;">
										{{ $chainInvoice->company->name ?? 'Unknown' }}
									</div>
								</div>
							</div>
						</div>
						<div class="flex items-center justify-between">
							<span class="text-xs">#{{ $chainInvoice->id }}</span>
							<x-filament::badge
								size="sm"
								color="{{ $statusColor }}">
								{{ $chainInvoice->status ? $chainInvoice->status : 'no status'}}
							</x-filament::badge>
						</div>
					</div>

					{{-- Client Info --}}
					@if($chainInvoice->client)
						<div class="mt-2 text-xs text-gray-500 dark:text-gray-300 text-center w-48 h-4 flex items-center justify-end"
							title="{{ $chainInvoice->client->name }}">
							<div class="truncate px-2" style="max-width: 100%;">
								@php
									$clientName = $chainInvoice->client->name;
									$maxLength = 25; // Limit to 25 characters
									if (strlen($clientName) > $maxLength) {
										$clientName = substr($clientName, 0, $maxLength - 3) . '...';
									}
								@endphp
								→ {{ $clientName }}
							</div>
						</div>
					@endif
				</div>
			</div>

			{{-- Arrow --}}
			@if($index < count($chainData) - 1)
				<div class="flex-shrink-0 text-gray-400 flex items-center px-2">
					<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
					</svg>
				</div>
			@endif
		@endforeach
		@php
			$lastClient = collect($chainData)->last()?->client;
		@endphp

		@if($lastClient)
			{{-- Client Final Box --}}

			<div class="flex-shrink-0 text-gray-400 flex items-center px-2">
				<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
				</svg>
			</div>
			<div class="flex-shrink-0 relative w-40">
				<div class="flex flex-col items-center w-full">
					<div class="px-2 py-3 rounded-lg border-2 text-center w-40 flex flex-col justify-between relative bg-gray-50 dark:bg-transparent border-gray-300 dark:border-gray-700 ">
						<div class="flex justify-start items-center gap-2">
							<x-filament::icon
								alias="panels::topbar.global-search.field"
								icon="heroicon-m-check-circle"
								wire:target="search"
								class="h-4 w-4 text-success-500  shrink-0"
							/>
							<div class="text-sm font-semibold leading-tight">
								{{ Str::limit($lastClient->name, 30) }}
							</div>
						</div>
					</div>
					<div class="mt-2 text-xs text-gray-500 dark:text-gray-300 text-center w-40 h-4 flex items-center justify-center">
						<div class="truncate px-2">
							End of Chaining
						</div>
					</div>
				</div>
			</div>
		@endif
	</div>

	{{-- Chain Summary --}}
	<div class="mt-3 pt-3 border-t border-gray-200">
		<div class="flex justify-between text-xs text-gray-500 dark:text-gray-300">
			<span>
				<strong>Your are here:</strong>
				@php
					$currentIndex = array_search($currentInvoiceId, array_column($chainData, 'id'));
				@endphp
				{{ $currentIndex === 0 ? 'Main Invoice' : 'Chain Level ' . ($currentIndex + 1) }}
				({{ $currentIndex + 1 }} of {{ $totalChain }} invoice chainings)
			</span>
			<span>
				<strong>Total Value:</strong>
				{{ $chainData[0]->currency->symbol ?? '$' }} {{ number_format($chainData[0]->invoice_amount ?? 0, 2) }}
			</span>
		</div>
	</div>

	{{-- Quick Actions --}}
	<div class="mt-3 pt-3 border-t border-gray-200">
		<div class="flex space-x-2">
			@if($invoice->parent_invoice_id)
				<x-filament::button
					color="info"
					size="xs"
					icon="heroicon-o-arrow-left-circle"
					href="{{ route('filament.admin.resources.invoices.view', $invoice->parent_invoice_id) }}"
					tag="a"
				>
					View Parent
				</x-filament::button>
			@endif

			@if($invoice->childInvoice()->exists())
				<x-filament::button
					color="info"
					size="xs"
					icon="heroicon-o-arrow-right-circle"
					icon-position="after"
					href="{{ route('filament.admin.resources.invoices.view', $invoice->childInvoice()->first()->id) }}"
					tag="a"
				>
					View Child
				</x-filament::button>
				{{-- <a href="{{ route('filament.admin.resources.invoices.view', $invoice->childInvoice()->first()->id) }}"
				class="inline-flex items-center px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded transition-colors">
					View Child
					<svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m-7-7h18"></path>
					</svg>
				</a> --}}
			@endif

			@if($totalChain === 1)
				<span class="inline-flex items-center px-2 py-1 text-xs bg-blue-100 text-blue-600 rounded">
					<svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
					</svg>
					Single Invoice (No Chain)
				</span>
			@endif
		</div>
	</div>
</div>
