<?php

namespace App\Http\Controllers\Admin;

use App\Exports\InvoicePerformanceExport;
use App\Http\Controllers\Controller;
use App\Models\Company;
use App\Models\CompanyBank;
use App\Models\Currency;
use App\Models\Invoice;
use App\Models\InvoiceDetail;
use App\Models\InvoiceTemplate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use Spatie\Browsershot\Browsershot;

class InvoiceController extends Controller
{
	public function invoicePayloads($record)
	{
		$invoice = Invoice::with([
			'company',
			'client',
			'currency',
			'invoiceDetails',
			'invoicetemplate.font',
			'companyBanks' => function ($query) {
				$query->where('include_in_invoice', true);
			}
		])->findOrFail($record);

		$bankFields = [
			'bank_acc_name',
			'bank_code',
			'bank_acc_no',
			'bank_acc_address',
			'bank_name',
			'bank_address',
			'bank_correspondent',
			'swift',
			'swift_correspondent',
			'remarks',
			'routing_no',
			'transit',
			'tt_charge',
			'iban',
			'institution',
			'bsb',
			'branch_code',
			'sort_code',
			'branch_bank',
			'back2back',
			'ABA',
			'IFSC',
			'custom_columns',
		];

		$banks = $invoice->companyBanks
			->map(function ($bank) use ($bankFields) {
				return collect($bank->only($bankFields))
					->filter(fn($val) => !is_null($val))
					->all();
			})
			->filter()
			->values();

		return [
			'templateVersion' => $invoice->company?->template_version,
			'invoice' => $invoice,
			'banks' => $banks,
		];
	}

	public function printInvoice($record, ?Request $request = null)
	{
		$payload = $this->invoicePayloads($record);
		$currentTemplate = $payload['templateVersion'];
		// Check if this is a preview request
		$isPreview = $request && $request->has('preview');
		Log::info($isPreview);
		if ($currentTemplate === 'v1') {
			$invTemp = $payload['invoice']->company->template;
			$template = view('invoice.' . $invTemp, [
				'payload' => $payload,
			])->render();

			$footer =
				'<div style="margin: 0 auto; text-align: center;">
					<div class="col-12" style="font-family: \'Courier New\', monospace; font-size: 11px;">
						<span class="text-center" style="font-size: 10px;">
							This is a digitally generated invoice, no authorization signature is required
						</span>
					</div>
				</div>';

			return view('invoice.' . $invTemp, compact('payload', 'footer'));
		}
		if ($currentTemplate === 'v2') {
			return view('template.v2.printPreview', compact('payload'));
		}
	}

	public function printInvoicePdf($record)
	{
		$payload = $this->invoicePayloads($record);
		$currentTemplate = $payload['templateVersion'];

		// Get invoice data for filename
		$invoice = $payload['invoice'];
		$filename = 'Invoice_' . $invoice->invoice_number . '.pdf';

		// Get layout configuration
		$layoutConfig = $invoice->invoicetemplate?->layout_config ?? [];

		// Paper size from config with custom F4 support
		$paperSize = $layoutConfig['paper_size'] ?? 'legal';
		$paperFormat = match (strtolower($paperSize)) {
			'a4' => 'A4',
			'letter' => 'Letter',
			'legal' => 'Legal',
			'f4' => 'custom', // F4 will use custom dimensions
			default => 'Legal'
		};

		// F4 dimensions: 210 × 330 mm
		$isF4 = strtolower($paperSize) === 'f4';
		$f4Width = 210; // mm
		$f4Height = 330; // mm

		// Margins from config (in mm) - ensure minimum margins for F4
		$marginTop = max($layoutConfig['margin_top'] ?? 10, $isF4 ? 10 : 5);
		$marginRight = max($layoutConfig['margin_right'] ?? 10, $isF4 ? 10 : 5);
		$marginBottom = max($layoutConfig['margin_bottom'] ?? 10, $isF4 ? 10 : 5);
		$marginLeft = max($layoutConfig['margin_left'] ?? 10, $isF4 ? 10 : 5);

		if ($currentTemplate === 'v1') {
			$invTemp = $payload['invoice']->company->template;
			$html = view('invoice.' . $invTemp, [
				'payload' => $payload,
			])->render();
		} else {
			$html = view('template.v2.printPreview', compact('payload'))->render();
		}
		$browsershot = Browsershot::html($html);
		if ($isF4) {
			$browsershot->paperSize($f4Width, $f4Height, 'mm');
		} else {
			$browsershot->format($paperFormat);
		}

		try {
			$optimalScale = $this->calculateOptimalScale($html, $paperSize, $isF4);

			$pdf = $browsershot
				->margins($marginTop, $marginRight, $marginBottom, $marginLeft)
				->noSandbox()
				->showBackground()
				->waitUntilNetworkIdle()
				->scale($optimalScale)
				->setOption('printBackground', true)
				->setOption('preferCSSPageSize', false)
				->setOption('displayHeaderFooter', false)
				->pdf();
			return response($pdf)
				->header('Content-Type', 'application/pdf')
				->header('Content-Disposition', 'inline; filename="' . $filename . '"');
		} catch (\Exception $e) {
			if ($currentTemplate === 'v1') {
				$invTemp = $payload['invoice']->company->template;
				return view('invoice.' . $invTemp, compact('payload'))
					->with('error', 'PDF generation failed. Showing preview instead.');
			} else {
				return view('template.v2.printPreview', compact('payload'))
					->with('error', 'PDF generation failed. Showing preview instead.');
			}
		}
	}

	//use API2PDF (https://v2.api2pdf.com/swagger/index.html | API KEY: 0117465c-6c24-49c0-a692-6bf9e79cf55f)
	/*
		public function printInvoiceApi2Pdf($record)
		{
			try {
				// Get invoice payload data
				$payload = $this->invoicePayloads($record);
				$currentTemplate = $payload['templateVersion'];
				$invoice = $payload['invoice'];

				// Generate HTML content based on template version
				if ($currentTemplate === 'v1') {
					$invTemp = $invoice->company->template;
					$html = view('invoice.' . $invTemp, [
						'payload' => $payload,
					])->render();
				} else {
					// V2 template
					$html = view('template.v2.printPreview', compact('payload'))->render();
				}

				Log::info('API2PDF Invoice Content Generated', [
					'record_id' => $record,
					'template_version' => $currentTemplate,
					'template_name' => $currentTemplate === 'v1' ? $invTemp : 'v2.printPreview',
					'html_length' => strlen($html),
					'invoice_number' => $invoice->invoice_no ?? 'N/A'
				]);

				// API2PDF configuration
				$apiKey = env('API2PDF_API_KEY', '0117465c-6c24-49c0-a692-6bf9e79cf55f');
				$apiUrl = 'https://v2.api2pdf.com/chrome/pdf/html';

				// Check if we should use test mode (simulate success without actual API call)
				$testMode = env('API2PDF_TEST_MODE', false);

				// Log API key info for debugging (without exposing full key)
				Log::info('API2PDF Configuration', [
					'api_url' => $apiUrl,
					'api_key_length' => strlen($apiKey),
					'api_key_prefix' => substr($apiKey, 0, 8) . '...',
					'test_mode' => $testMode
				]);

				// If test mode is enabled, simulate successful PDF generation
				if ($testMode) {
					Log::info('API2PDF Test Mode - Simulating Success', [
						'record_id' => $record,
						'invoice_number' => $invoice->invoice_no ?? 'N/A'
					]);

					// Create a simple mock PDF content (just for testing)
					$mockPdf = '%PDF-1.4 Mock PDF for testing API2PDF integration with invoice ' . ($invoice->invoice_no ?? $record);

					$filename = 'Invoice_' . ($invoice->invoice_no ?? $record) . '_API2PDF_Mock.pdf';
					return response($mockPdf)
						->header('Content-Type', 'application/pdf')
						->header('Content-Disposition', 'inline; filename="' . $filename . '"');
				}

				// Get layout configuration for paper size and margins
				$layoutConfig = $invoice->invoicetemplate?->layout_config ?? [];
				$paperSize = $layoutConfig['paper_size'] ?? 'legal';
				$marginTop = $layoutConfig['margin_top'] ?? 20;
				$marginRight = $layoutConfig['margin_right'] ?? 20;
				$marginBottom = $layoutConfig['margin_bottom'] ?? 20;
				$marginLeft = $layoutConfig['margin_left'] ?? 20;

				// Convert paper size to API2PDF format
				$api2pdfFormat = match(strtolower($paperSize)) {
					'a4' => 'A4',
					'letter' => 'Letter',
					'legal' => 'Legal',
					'f4' => 'A4', // F4 fallback to A4 for API2PDF
					default => 'Legal'
				};

				// Generate filename based on invoice data
				$filename = 'Invoice_' . ($invoice->invoice_no ?? $record) . '_API2PDF.pdf';

				// Prepare API payload for API2PDF
				$apiPayload = [
					'html' => $html,
					'inlinePdf' => true,
					'fileName' => $filename,
					'options' => [
						'landscape' => false,
						'printBackground' => true,
						'format' => $api2pdfFormat,
						'margin' => [
							'top' => $marginTop . 'mm',
							'right' => $marginRight . 'mm',
							'bottom' => $marginBottom . 'mm',
							'left' => $marginLeft . 'mm'
						]
					]
				];

				Log::info('API2PDF Invoice Request', [
					'record_id' => $record,
					'invoice_number' => $invoice->invoice_no ?? 'N/A',
					'api_url' => $apiUrl,
					'paper_size' => $paperSize,
					'api2pdf_format' => $api2pdfFormat,
					'filename' => $filename,
					'html_length' => strlen($html),
					'payload_keys' => array_keys($apiPayload)
				]);

				// Make API request using Laravel HTTP client
				$response = Http::withHeaders([
					'Authorization' => $apiKey,
					'Content-Type' => 'application/json'
				])
				->timeout(30)
				->post($apiUrl, $apiPayload);

				if ($response->successful()) {
					$responseData = $response->json();

					// API2PDF returns a JSON response with PDF URL or base64 data
					if (isset($responseData['pdf'])) {
						// If PDF is returned as base64
						$pdf = base64_decode($responseData['pdf']);
					} elseif (isset($responseData['FileUrl'])) {
						// If PDF is returned as URL, download it
						$pdfResponse = Http::get($responseData['FileUrl']);
						$pdf = $pdfResponse->body();
					} else {
						throw new \Exception('Invalid API2PDF response format');
					}

					Log::info('API2PDF Invoice Success', [
						'record_id' => $record,
						'invoice_number' => $invoice->invoice_no ?? 'N/A',
						'pdf_size' => strlen($pdf),
						'filename' => $filename,
						'response_keys' => array_keys($responseData)
					]);

					// Return PDF response
					return response($pdf)
						->header('Content-Type', 'application/pdf')
						->header('Content-Disposition', 'inline; filename="' . $filename . '"');

				} else {
					$errorData = $response->json();
					$errorMessage = $errorData['error'] ?? $errorData['message'] ?? 'Unknown API2PDF error';

					Log::error('API2PDF Invoice API Error', [
						'record_id' => $record,
						'invoice_number' => $invoice->invoice_no ?? 'N/A',
						'status' => $response->status(),
						'error' => $errorMessage,
						'response' => $response->body()
					]);

					throw new \Exception("API2PDF API Error: {$errorMessage}");
				}

			} catch (\Exception $e) {
				Log::error('API2PDF Invoice Failed', [
					'record_id' => $record,
					'invoice_number' => $invoice->invoice_no ?? 'N/A',
					'error' => $e->getMessage()
				]);

				// Fallback to HTML preview instead of PDF
				if ($currentTemplate === 'v1') {
					$invTemp = $invoice->company->template;
					return view('invoice.' . $invTemp, compact('payload'))
						->with('error', 'API2PDF generation failed. Showing preview instead.');
				} else {
					return view('template.v2.printPreview', compact('payload'))
						->with('error', 'API2PDF generation failed. Showing preview instead.');
				}
			}
		}
	*/

	/**
	 * Calculate optimal scale to fit content in single page based on content analysis
	 */
	private function calculateOptimalScale($html, $paperSize, $isF4)
	{
		try {
			// Analyze HTML content to estimate required scale
			$contentMetrics = $this->analyzeHtmlContent($html);

			// Base scale factors for different paper sizes
			$baseScales = [
				'f4' => 0.85,     // F4 is taller, can accommodate more content
				'legal' => 0.8,   // Legal size
				'a4' => 0.75,     // A4 is smaller
				'letter' => 0.75  // Letter size
			];

			$baseScale = $baseScales[strtolower($paperSize)] ?? 0.8;

			// Adjust scale based on content complexity
			$adjustmentFactor = 1.0;

			// Factor 1: Table rows (more rows = need smaller scale)
			if ($contentMetrics['table_rows'] > 20) {
				$adjustmentFactor *= 0.8;
			} elseif ($contentMetrics['table_rows'] > 15) {
				$adjustmentFactor *= 0.85;
			} elseif ($contentMetrics['table_rows'] > 10) {
				$adjustmentFactor *= 0.9;
			}

			// Factor 2: Content length (longer content = need smaller scale)
			if ($contentMetrics['content_length'] > 50000) {
				$adjustmentFactor *= 0.85;
			} elseif ($contentMetrics['content_length'] > 30000) {
				$adjustmentFactor *= 0.9;
			}

			// Factor 3: Number of sections/divs (more complex layout = smaller scale)
			if ($contentMetrics['div_count'] > 50) {
				$adjustmentFactor *= 0.9;
			}

			// Calculate final scale
			$finalScale = $baseScale * $adjustmentFactor;

			// Ensure scale is within reasonable bounds
			$finalScale = max(0.5, min(1.0, $finalScale));

			Log::info('Scale calculation completed', [
				'paper_size' => $paperSize,
				'base_scale' => $baseScale,
				'adjustment_factor' => $adjustmentFactor,
				'final_scale' => $finalScale,
				'content_metrics' => $contentMetrics
			]);

			return $finalScale;
		} catch (\Exception $e) {
			Log::warning('Scale calculation failed, using default scale', [
				'error' => $e->getMessage()
			]);
			// Fallback to conservative scale based on paper size
			return $isF4 ? 0.8 : 0.75;
		}
	}

	/**
	 * Analyze HTML content to extract metrics for scale calculation
	 */
	private function analyzeHtmlContent($html)
	{
		$metrics = [
			'content_length' => strlen($html),
			'table_rows' => 0,
			'div_count' => 0,
			'image_count' => 0
		];

		// Count table rows
		$metrics['table_rows'] = substr_count($html, '<tr');

		// Count divs (complexity indicator)
		$metrics['div_count'] = substr_count($html, '<div');

		// Count images
		$metrics['image_count'] = substr_count($html, '<img');

		return $metrics;
	}

	public function invoicePerformancePayload(?string $startDate = null, ?string $endDate = null, ?string $companyId = null): array
	{
		$query = Invoice::with(['parentInvoice', 'company', 'client', 'currency'])
			->whereNotNull('parent_invoice_id');

		if ($startDate && $endDate) {
			$query->whereHas('parentInvoice', function ($q) use ($startDate, $endDate) {
				$q->whereBetween('created_at', [$startDate, $endDate]);
			});
		}

		if (!empty($companyId)) {
			$query->where('company_id', $companyId);
		}

		$children = $query->orderby('created_at', 'asc')->get();
		$groupedByRoot = $children->groupBy(function ($child) {
			$root = $child;
			while ($root->parentInvoice) {
				$root = $root->parentInvoice;
			}
			return $root->id;
		});


		$report = $groupedByRoot->flatMap(function ($group) {
			$sorted = $group->sortBy('created_at')->values();
			$lastChild = $sorted->last();

			return $sorted->map(function ($child) use ($lastChild) {
				$rootParent = $child;
				while ($rootParent->parentInvoice) {
					$rootParent = $rootParent->parentInvoice;
				}

				$isLastChild = $child->id === $lastChild->id;
				$feeAmount = $isLastChild ? 10 : ($child->booking_fee ?? 0);
				$grossProfit = $feeAmount * ($child->rates ?? 0);
				$bgColor = is_null($child->order_id) ? 'bg-yellow-50' : 'bg-transparent';

				return [
					'invoice_no'     => $child?->invoice_no,
					'invoice_date'   => $child?->invoice_date,
					'created_at'  	 => $child?->created_at,
					'client_id'      => $child->client_id,
					'client_name'    => $child?->client?->name,
					'nama_pt_id'     => $child->company_id,
					'nama_pt_name'   => $child?->company?->name,
					'amount'         => $child->invoice_amount,
					'overseas_agent' => $rootParent?->company?->name,
					'rate'           => $child->rates,
					'currency'       => $rootParent->currency->symbol ?? '',
					'fee_amount'     => $feeAmount,
					'gross_profit'   => $grossProfit,
					'bgColor'		=> $bgColor,
				];
			});
		});


		// Data untuk line chart: daily amount dan profit (exclude weekend)
		$dailyChart = $report
			// ->filter(fn ($item) => !in_array(\Carbon\Carbon::parse($item['invoice_date'])->dayOfWeek, [6, 0])) // 6: Saturday, 0: Sunday
			->groupBy('invoice_date')
			->map(fn($group) => [
				'amount' => collect($group)->sum('amount'),
				'gross_profit' => collect($group)->sum('gross_profit'),
			])
			->sortKeys()
			->toArray();

		Log::info('Invoice Performance Payload', [
			'daily chart' => $dailyChart,
		]);

		return [
			'report' => $report->toArray(),
			'daily_chart' => $dailyChart,
		];
	}

	public function printInvoicePerformancePdf(Request $request)
	{
		$start = $request->query('start_date');
		$end = $request->query('end_date');
		$companyId = $request->query('company_id');

		// Ambil payload lengkap: report, chart, dan metadata
		$payload = $this->invoicePerformancePayload($start, $end, $companyId);

		$report = $payload['report'] ?? [];
		$dailyChart = $payload['daily_chart'] ?? [];
		$totalAmount = collect($report)->sum('amount');
		$totalProfit = collect($report)->sum('gross_profit');
		$totalInvoices = count($report);

		$html = view('template.v2.invoice-performance-pdf', [
			'report'        => $report,
			'dailyChart'    => $dailyChart,
			'totalAmount'   => $totalAmount,
			'totalProfit'   => $totalProfit,
			'totalInvoices' => $totalInvoices,
			'dateStart'     => $start,
			'dateEnd'       => $end,
			'fontFamily'    => 'Inter',
			'fontSource'    => 'https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap',
			'paperSize'     => 'Legal',
			'marginTop'     => 5,
			'marginRight'   => 10,
			'marginBottom'  => 10,
			'marginLeft'    => 10,
		])->render();

		// Footer untuk browsershot
		$footerHtml = '<div style="font-family: Courier, monospace; font-size:10px; width:100%; text-align:center; margin:0 auto;">
			Generated by Rebrandz @ ' . now()->translatedFormat('d F Y') . ' &nbsp;&nbsp;|&nbsp;&nbsp; Page <span class="pageNumber"></span> of <span class="totalPages"></span>
		</div>';

		try {
			$pdf = Browsershot::html($html)
				->format('Legal')
				->margins(10, 10, 10, 10)
				->noSandbox()
				->scale(0.85)
				->showBackground()
				->waitUntilNetworkIdle()
				->setOption('printBackground', true)
				->setOption('preferCSSPageSize', false)
				->setOption('displayHeaderFooter', true)
				->setOption('headerTemplate', '<div></div>')
				->setOption('footerTemplate', $footerHtml)
				->pdf();

			return response($pdf)
				->header('Content-Type', 'application/pdf')
				->header('Content-Disposition', 'inline; filename="Invoice_Performance_Report.pdf"');
		} catch (\Exception $e) {
			return $html; // fallback debug
		}
	}

	public function printInvoicePerformancePreview(Request $request)
	{
		$start = $request->query('start_date');
		$end = $request->query('end_date');

		// Ambil payload lengkap: report, chart, dan metadata
		$payload = $this->invoicePerformancePayload($start, $end);

		$report = $payload['report'] ?? [];
		$dailyChart = $payload['daily_chart'] ?? [];
		$ptChart = $payload['pt_chart'] ?? [];

		$totalAmount = collect($report)->sum('amount');
		$totalProfit = collect($report)->sum('gross_profit');
		$totalInvoices = count($report);

		return view('template.v2.invoice-performance-pdf', [
			'report'        => $report,
			'dailyChart'    => $dailyChart,
			'ptChart'       => $ptChart,
			'totalAmount'   => $totalAmount,
			'totalProfit'   => $totalProfit,
			'totalInvoices' => $totalInvoices,
			'dateStart'     => $start,
			'dateEnd'       => $end,
			'fontFamily'    => 'Inter',
			'fontSource'    => 'https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap',
			'paperSize'     => 'Legal',
			'marginTop'     => 5,
			'marginRight'   => 10,
			'marginBottom'  => 10,
			'marginLeft'    => 10,
		]);
	}

	public function exportInvoicePerformanceExcel(Request $request)
	{
		$start = $request->query('start_date');
		$end = $request->query('end_date');

		$payload = $this->invoicePerformancePayload($start, $end);
		$report = collect($payload['report']);

		return Excel::download(new InvoicePerformanceExport($report), 'invoice_performance.xlsx');
	}
}
