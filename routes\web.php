<?php

use App\Http\Controllers\Admin\CompanyFinancialController;
use App\Http\Controllers\Admin\InvoiceController;
use App\Http\Controllers\Admin\McCustomerClosingController;
use App\Http\Controllers\Admin\McOrderController;
use App\Http\Controllers\Admin\OrderController;
use App\Http\Controllers\BriSlipKliringController;
use App\Models\Font;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    // return view('welcome'); // Keep for future reference
    return redirect()->route('login');
});

Route::middleware(['auth:sanctum', config('jetstream.auth_session'), 'verified',])->group(function () {
    Route::get('/dashboard', function () {
        return view('dashboard');
    })->name('dashboard');
	Route::redirect('/dashboard', '/admin');

    // Laravel Pulse routes are automatically registered by the package at /pulse

    // Management routes (previously Admin routes)
    Route::middleware(['role:Super Admin'])->prefix('management')->name('management.')->group(function () {
        Route::get('/dashboard', [App\Http\Controllers\Admin\DashboardController::class, 'index'])->name('dashboard');
        Route::resource('users', App\Http\Controllers\Admin\UserController::class);

        // Health Dashboard
        Route::get('/health', [App\Http\Controllers\Admin\HealthController::class, 'index'])->name('health.index');
        Route::post('/health/run-checks', [App\Http\Controllers\Admin\HealthController::class, 'runChecks'])->name('health.run-checks');
        Route::get('/health/settings', [App\Http\Controllers\Admin\HealthController::class, 'getSettings'])->name('health.settings');
        Route::post('/health/toggle-setting', [App\Http\Controllers\Admin\HealthController::class, 'toggleSetting'])->name('health.toggle-setting');
    });
	Route::get('/{record}/printInvoice', [InvoiceController::class, 'printInvoice'])->name('printInvoice');
	Route::get('/{record}/printInvoicePdf', [InvoiceController::class, 'printInvoicePdf'])->name('printInvoicePdf');
	// Route::get('/{record}/printInvoiceApi2Pdf', [InvoiceController::class, 'printInvoiceApi2Pdf'])->name('printInvoiceApi2Pdf');
	Route::get('/brislipkliring', [BriSlipKliringController::class, 'slipPdf'])->name('slipPdf');

	Route::group(['prefix' => 'report', 'as' => 'report.'], function () {
		Route::get('/getDashboardStatPayload', [OrderController::class, 'getDashboardStatPayload'])->name('getDashboardStatPayload');
		Route::get('/getStackedCashFlowPayload', [OrderController::class, 'getStackedCashFlowPayload'])->name('getStackedCashFlowPayload');
		Route::get('/getTrendsPayload', [OrderController::class, 'getTrendsPayload'])->name('getTrendsPayload');
		Route::get('/getChartPayload', [OrderController::class, 'getChartPayload'])->name('getChartPayload');
		Route::get('/printDashboardPdf', [OrderController::class, 'printDashboardPdf'])->name('printDashboardPdf');


		Route::get('/printInvoicePerformancePdf', [InvoiceController::class, 'printInvoicePerformancePdf'])->name('grossProfitInvoicePdf');
		Route::get('/printInvoicePerformancePreview', [InvoiceController::class, 'printInvoicePerformancePreview'])->name('grossProfitInvoicePreview');
		// Route::get('/printInvoicePerformanceExcel', [InvoiceController::class, 'exportInvoicePerformanceExcel'])->name('grossProfitPerformanceXlsx');
		// Route::get('/printFinancialPerformancePdf', [CompanyFinancialController::class, 'printFinancialPerformancePdf'])->name('financialPerformancePdf');

		Route::get('/printOrderRevenuePdf', [OrderController::class, 'printOrderRevenuePdf'])->name('dailyOrderProfitPdf');
		Route::get('/printOrderRevenuePreview', [OrderController::class, 'printOrderRevenuePreview'])->name('dailyOrderProfitPreview');
		// Route::get('/orderPerformancePayload', [OrderController::class, 'orderPerformancePayload'])->name('orderPerformancePayload');
		// Route::get('/printOrderTransactionPdf', [OrderController::class, 'printOrderTransactionPdf'])->name('printOrderTransactionPdf');


	});

	Route::get('/recalc-customer-balance', [McCustomerClosingController::class, 'recalcMcCustomerBalances'])->name('recalcMcCustomerBalances');
	//create mc snapshot
	Route::get('/snapshot-customer-order', [McCustomerClosingController::class, 'mcCustomerOrderSnapshot']);
	Route::get('/snapshot-customer-deposit', [McCustomerClosingController::class, 'mcCustomerDepositSnapshot']);

	//do mc closing
	Route::get('/closing-customer-starter', [McCustomerClosingController::class, 'mcCustomerClosingStarter']); //delete/comment to prevent run more than once
	Route::get('/closing-customer-order', [McCustomerClosingController::class, 'mcCustomerClosingOrder']);
	Route::get('/closing-customer-deposit', [McCustomerClosingController::class, 'mcCustomerClosingDeposit']);

	//mc order
	Route::get('/mc/mc-orders/{record}/preview', [McOrderController::class, 'printOrder'])->name('mcOrderPreview');

	// Route::get('/invoice/view', function () {
	// 	$font = Font::where('name', 'Poppins')->first();
    //     return view('/invoice/dynamic_tailwind', compact('font'));
    // })->name('invoiceview');
});
