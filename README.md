# Laravel Starterkit Project

Proyek ini adalah implementasi dari <PERSON> Starterkit sesuai dengan arsitektur yang telah ditentukan.

## Stack Backend

-   Laravel sebagai Backend Framework
-   Livewire sebagai frontend framework untuk kegiatan administrasi dan pengelolaan data
-   Laravel Sanctum untuk autentikasi API
-   Laravel Pulse untuk monitoring aplikasi
-   Laravel Scout untuk pencarian
-   Spatie Role Permission untuk manajemen role dan permission
-   Spatie Activity Log untuk logging aktivitas
-   Spatie Media Library untuk manajemen media
-   Spatie <PERSON> Health untuk monitoring kesehatan aplikasi
-   Spatie Laravel Settings untuk manajemen pengaturan
-   <PERSON><PERSON> Honey Pot untuk proteksi form
-   <PERSON><PERSON> Schedule Monitor untuk monitoring jadwal
-   Spatie Laravel Backup untuk backup
-   MySQL sebagai database
-   Laravel Cashier untuk manajemen pembayaran
-   Laravel Cashier Paypal untuk integrasi dengan Paypal
-   Scribe untuk dokumentasi API

## Stack Frontend

### Web Admin

-   Livewire
-   Alpine.js
-   Tailwind CSS
-   Flowbite

## Instalasi

1. Clone repository

```bash
git clone <repository-url>
```

2. Masuk ke direktori proyek

```bash
cd laravel-starterkit-project
```

3. Instal dependensi PHP

```bash
composer install
```

4. Instal dependensi JavaScript

```bash
npm install
```

5. Salin file .env.example ke .env

```bash
cp .env.example .env
```

6. Generate application key

```bash
php artisan key:generate
```

7. Konfigurasi database di file .env

```
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=laravel_starterkit_project
DB_USERNAME=root
DB_PASSWORD=
```

8. Jalankan migrasi database

```bash
php artisan migrate
```

9. Jalankan seeder (opsional)

```bash
php artisan db:seed
```

10. Kompilasi aset frontend

```bash
npm run build
```

11. Jalankan server development

```bash
php artisan serve
```

## Fitur

-   Autentikasi pengguna (login, register, reset password)
-   Manajemen pengguna
-   Manajemen role dan permission
-   Manajemen tim
-   Manajemen media
-   Monitoring aplikasi
-   Monitoring kesehatan aplikasi
-   Monitoring jadwal
-   Backup
-   API dengan dokumentasi
-   Integrasi pembayaran

## Lisensi

Proyek ini dilisensikan di bawah [MIT License](LICENSE).

# migrasi

## clear migrations
## jalankan export db lama
	- table mc_bank_groups
	- table mc_banks
	- table mc_customers
	- table mc_orders
	- table mc_deposits

## hapus table local
	- table master_invoice_details
	- table mc_bank_groups
	- table mc_banks
	- table mc_customers
	- table mc_orders
	- table mc_deposits

## table business_types

-   tambahkan kolom deleted_at
-	export database

## table companies

-   jalankan migrasi add column companies
    2025_07_09_235903_add_columns_to_companies_table
    remove column bank info
-	export db

## table invoice_banks

-   tambahkan kolom:
    a include_in_invoice,
    b custom_columns
    c ganti nama menjadi company_banks
    d insert deleted_at
	e export db

## table invoices

-   jalankan migrasi
    2025_07_10_001952_add_columns_to_invoices_table
-   jalankan sql
    rename field invoice_due => due_date;
    jalankan 'php artisan app:set-parent-invoice-id'
    jalankan 'php artisan app:set-booking-fee-from-detail'
    jalankan 'php artisan app:set-client-id'

## model Agent

-   create model agent di FE

## table mc_customers

-   export mc_customers di db lama
-   import ke db baru
-   tambahkan kolom agent_id after id
-   set default agent_id menjadi 1
-   jalankan clear customer code di db baru
    UPDATE web_starter_kit.mc_customers
    SET customer_code = NULL;
    SET total_orders = null;
    SET total_deposits = null;
    SET balance = null;
-   jalankan create new customer code:
    php artisan tinker
    copy dan paste isi dari reset_customer_code.txt ke terminal

## table mc_orders

-   export lalu import mc_orders ke db baru
-   insert column order_date type DATE nullable
-   jalankan sql:
    UPDATE web_starter_kit.mc_orders
    SET order_date = DATE(created_at);
-   jalankan mcCustomerOrderSnapshot

## table mc_deposits

-   export lalu import mc_deposits ke db baru
-   insert column trx_type, default 'incoming'
-   setelah berhasil, hapus default 'incoming' menjadi NULL
-   jalankan mcDepositSnapshot

## closing McOrder & McDeposit

-   wajib menjalankan snapshot
-   jalankan mcCustomerClosingStarter

## rekalkulasi Deposit di customer

-   jalankan recalcMcCustomerBalances
-   bulk:
    BulkAction::make('Recalculate Balances')
    ->icon('heroicon-m-calculator')
    ->action(function (Collection $records) {
        $customerIds = $records->pluck('id')->toArray();
        \App\Services\RecalculateCustomerBalanceService::run($customerIds);

            Notification::make()
                ->title('Saldo semua customer berhasil dihitung ulang.')
                ->success()
                ->send();
        })

-   record:
    Action::make('Recalculate Balance')
    ->icon('heroicon-m-arrow-path')
    ->color('primary')
    ->requiresConfirmation()
    ->action(function ($record) {
                    // Jalankan service
                    \App\Services\RecalculateCustomerBalanceService::run($record->id);

                        Notification::make()
                            ->title('Saldo berhasil dihitung ulang.')
                            ->success()
                            ->send();
                    }),

## create OCR Shortcut


#### tambah filter per company L2
#### urut asc 
