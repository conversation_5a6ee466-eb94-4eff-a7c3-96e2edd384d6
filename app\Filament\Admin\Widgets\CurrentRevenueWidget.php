<?php

namespace App\Filament\Admin\Widgets;

use App\Models\Company;
use App\Models\CompanyDepositSummary;
use App\Models\Invoice;
use App\Models\Order;
use Carbon\Carbon;
use Filament\Forms\Components\Select;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Number;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class CurrentRevenueWidget extends BaseWidget
{
	protected int | string | array $columnSpan = 3;

	protected static string $view = 'filament.admin.widgets.current-revenue-widget';

	public function getViewData(): array
	{
		return Cache::remember('dashboard.revenue.summary', now()->addMinutes(5), function () {
			$now = now('Asia/Jakarta');

			$calcRevenue = fn($query) => $query
				->selectRaw('SUM(((sell_rates - rates) * total) + charges) AS revenue')
				->value('revenue') ?? 0;

			$baseQuery = fn() => Order::query(); // cukup query() saja, tidak perlu with() jika tidak dipakai

			// Pendefinisian waktu
			$thisYear      = $now->year;
			$lastYear      = $now->copy()->subYear()->year;

			$thisMonth     = $now->month;
			$lastMonthDate = $now->copy()->subMonth();
			$lastMonth     = $lastMonthDate->month;
			$lastMonthYear = $lastMonthDate->year;

			$thisWeekStart = $now->copy()->startOfWeek();
			$thisWeekEnd   = $now->copy()->endOfWeek();

			$lastWeekStart = $now->copy()->subWeek()->startOfWeek();
			$lastWeekEnd   = $now->copy()->subWeek()->endOfWeek();

			$today         = $now->toDateString();
			$yesterday     = $now->copy()->subDay()->toDateString();

			// Perhitungan Revenue
			$revenueThisYear   = $calcRevenue(($baseQuery())->whereYear('order_date', $thisYear));
			$revenueLastYear   = $calcRevenue(($baseQuery())->whereYear('order_date', $lastYear));

			$revenueThisMonth  = $calcRevenue(($baseQuery())
				->whereYear('order_date', $thisYear)
				->whereMonth('order_date', $thisMonth));

			$revenueLastMonth  = $calcRevenue(($baseQuery())
				->whereYear('order_date', $lastMonthYear)
				->whereMonth('order_date', $lastMonth));

			$revenueThisWeek   = $calcRevenue(($baseQuery())
				->whereBetween('order_date', [$thisWeekStart, $thisWeekEnd]));

			$revenueLastWeek   = $calcRevenue(($baseQuery())
				->whereBetween('order_date', [$lastWeekStart, $lastWeekEnd]));

			$revenueToday      = $calcRevenue(($baseQuery())->whereDate('order_date', $today));
			$revenueYesterday  = $calcRevenue(($baseQuery())->whereDate('order_date', $yesterday));

			// Perhitungan Persentase Perbandingan
			$percent = fn($now, $past) => $now !== 0 ? round((($now - $past) / $now) * 100, 2) : 0;

			return [
				'revenue' => [
					'year'  => $revenueThisYear,
					'month' => $revenueThisMonth,
					'week'  => $revenueThisWeek,
					'day'   => $revenueToday,
				],
				'difference' => [
					'year'  => $percent($revenueThisYear, $revenueLastYear),
					'month' => $percent($revenueThisMonth, $revenueLastMonth),
					'week'  => $percent($revenueThisWeek, $revenueLastWeek),
					'day'   => $percent($revenueToday, $revenueYesterday),
				],
				'datenumbering' => [
					'year'  => substr((string) $now->year, -2),
					'month' => str_pad((string) $now->month, 2, '0', STR_PAD_LEFT),
					'week'  => str_pad((string) $now->weekOfYear, 2, '0', STR_PAD_LEFT),
					'day'   => str_pad((string) $now->day, 2, '0', STR_PAD_LEFT),
				],
			];
    	});
	}


	public static function canAccess(): bool
	{
		return Auth::user()->hasAnyRole(['admin', 'Super Admin']);
	}
	public static function canView(): bool
	{
		return Auth::user()->hasAnyRole(['admin', 'Super Admin']);
	}
}
