@props(['invoice', 'mode' => 'view'])

@php
    // Build chain data for navigation
    $chainData = [];
    $current = $invoice;

    // Go to root (main invoice)
    while ($current->parent_invoice_id) {
        $current = $current->parentInvoice;
    }

    // Build chain from root
    $chainData[] = $current;
    while ($current->childInvoice()->exists()) {
        $current = $current->childInvoice()->first();
        $chainData[] = $current;
    }

    $currentInvoiceId = $invoice->id;
    $currentIndex = array_search($currentInvoiceId, array_column($chainData, 'id'));
@endphp

<div>
    <p class="text-md text-gray-500 mb-6">Review this Invoice before printed to PDF</p>
</div>

<div class="bg-white dark:bg-transparent border border-gray-200 rounded-lg p-2 space-y-3">
    <div class="items-center justify-between space-y-2">
        <div class="flex items-center space-x-5">
            <div class="flex items-center space-x-1 me-5">
                <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                </svg>
                <span class="text-sm font-medium text-gray-700 dark:text-gray-200 ms-2 pe-3">Chain Navigation: </span>
            </div>

            <div class="flex items-center space-x-3">
                @foreach($chainData as $index => $chainInvoice)
                    {{-- Navigation Button --}}
                    @if($chainInvoice->id === $currentInvoiceId)
                        <span class="px-3 py-1 text-xs font-bold rounded border-2"
                              style="background-color: #dbeafe; border-color: #2563eb; color: #1e3a8a;">
                            {{ $index === 0 ? 'Main' : 'L' . ($index + 1) }}
                        </span>
                    @else
                        @php
                            $companyName = $chainInvoice->company->name ?? 'Unknown';
                            $words = explode(' ', $companyName);
                            $shortName = count($words) <= 2 ? $companyName : implode(' ', array_slice($words, 0, 2));
                            if (strlen($shortName) > 15) {
                                $shortName = substr($shortName, 0, 12) . '...';
                            }
                        @endphp
                        <a href="{{ route('filament.admin.resources.invoices.' . $mode, $chainInvoice->id) }}"
                           class="px-3 py-1 text-xs font-medium rounded border transition-colors"
                           style="background-color: #f3f4f6; border-color: #d1d5db; color: #374151;"
                           onmouseover="this.style.backgroundColor='#e5e7eb'"
                           onmouseout="this.style.backgroundColor='#f3f4f6'"
                           title="Go to {{ $index === 0 ? 'Main Invoice' : 'Chain Level ' . ($index + 1) }} - {{ $companyName }}">
                            {{ $index === 0 ? 'Main' : 'L' . ($index + 1) }}
                        </a>
                    @endif

                    {{-- Arrow --}}
                    @if($index < count($chainData) - 1)
                        <svg class="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    @endif
                @endforeach
            </div>
        </div>

        <div class="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-200">
            <span>Position: {{ $currentIndex + 1 }} of {{ count($chainData) }}</span>
            @if(count($chainData) > 1)
                <span class="text-gray-300">|</span>
                <span>Total Value: {{ $chainData[0]->currency->symbol ?? '$' }} {{ number_format($chainData[0]->invoice_amount ?? 0, 2) }}</span>
            @endif
        </div>
    </div>

    {{-- Quick Actions --}}
    <div class="mt-2 pt-3 border-t border-gray-200">
        <div class="flex items-center justify-between">
            <div class="flex space-x-1">
                {{-- Previous/Next Navigation --}}
                @if($currentIndex > 0)
				<x-filament::button
					tag="a"
					size="xs"
					{{-- color="gray" --}}
					icon="heroicon-m-chevron-left"
					href="{{ route('filament.admin.resources.invoices.' . $mode, $chainData[$currentIndex - 1]->id) }}"
				>
					Previous
				</x-filament::button>
                @endif

                @if($currentIndex < count($chainData) - 1)
					<x-filament::button
						tag="a"
						size="xs"
						{{-- color="gray" --}}
						icon-position="after"
						icon="heroicon-m-chevron-right"
						href="{{ route('filament.admin.resources.invoices.' . $mode, $chainData[$currentIndex + 1]->id) }}"
					>
						Next
					</x-filament::button>
                @endif

                {{-- Jump to Main --}}
                @if($currentIndex > 0)
					<x-filament::button outlined
						tag="a"
						size="xs"
						color="warning"
						icon="heroicon-o-home"
						href="{{ route('filament.admin.resources.invoices.' . $mode, $chainData[0]->id) }}"
					>
						Jump to Main
					</x-filament::button>
                @endif
            </div>

            {{-- Chain Summary --}}
            <div class="">
                @if(count($chainData) === 1)
					<x-filament::badge
						color="warning"
						icon="heroicon-o-bolt">
						Single Invoice
					</x-filament::badge>
                @else

					<x-filament::badge
						color="success"
						icon="heroicon-o-link">
						{{ count($chainData) }} Chains
					</x-filament::badge>
                @endif
            </div>
        </div>
    </div>
</div>
