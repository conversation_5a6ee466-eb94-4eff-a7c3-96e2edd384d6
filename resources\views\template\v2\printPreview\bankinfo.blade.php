@php
	// Extract configuration
	$invoiceTemplate = $payload['invoice']->invoicetemplate;
	$layoutConfig = $invoiceTemplate?->layout_config ?? [];
	$banks = $payload['banks'];

	// Bank info configuration
	$bankInfoLayout = $layoutConfig['bankInfoLayout'] ?? 'tabular';
	$bankInfoWeight = $layoutConfig['bankInfoWeight'] ?? 'font-normal';
	$bankInfoBorder = $layoutConfig['bankInfoBorder'] ?? false;
	$bankInfoBg = $layoutConfig['bankInfoBg'] ?? 'transparent';
	$bankInfoDecor = $layoutConfig['bankInfoDecor'] ?? '';

	// Use isDarkColor function from parent (printPreview.blade.php)
	$bankInfoTextColorClass = isDarkColor($bankInfoBg) ? 'text-white' : 'text-black';

	// Determine column size from repeater layout
	$bankColumnSize = 2; // Default
	$repeaterLayout = $layoutConfig['repeater_layout'] ?? [];
	foreach ($repeaterLayout as $row) {
		$col1 = (array) ($row['column_1_content'] ?? []);
		if (in_array('bank_info', $col1)) {
			$bankColumnSize = $row['column_size'] ?? 2;
			break;
		}
	}
@endphp

{{-- Bank Information Section --}}
@if(!empty($banks))
	<div class="bank-info-section">
		<h3 class="mb-2">Bank Information:</h3>
		@if($bankInfoLayout === 'tabular')
			{{-- Tabular Layout (Most Common) --}}
			@if($bankColumnSize == 1)
				<div class="grid grid-cols-1 md:grid-cols-2 gap-3">
					@foreach ($banks as $bank)
						<div class="{{ $bankInfoBorder ? 'border border-gray-300' : '' }} {{ $bankInfoTextColorClass }}" style="background-color: {{ $bankInfoBg }}">
							<table class="w-full text-xs-compact {{ $bankInfoDecor }} border-collapse">
								@foreach ($bank as $key => $value)
									@if(!in_array($key, ['id', 'company_id', 'is_default', 'include_in_invoice', 'custom_columns', 'swift', 'swift_correspondent']) && !is_null($value) && $value !== '')
										<tr>
											<td class="border border-gray-300 p-1" width="120px">
												{{ ucwords(str_replace('_', ' ', $key)) }}
											</td>
											<td class="border border-gray-300 p-1 {{ $bankInfoWeight }}">
												@if(str_contains($key, 'address') && str_contains($value, '<'))
													{!! strip_tags($value, '<p><br><strong><b><em><i><u>') !!}
												@elseif(is_array($value))
													{{-- Handle custom columns --}}
													@if(isset($value['type']) && isset($value['value']))
														@if($value['type'] === 'richtext')
															{!! strip_tags($value['value'], '<p><br><strong><b><em><i><u>') !!}
														@else
															{{ $value['value'] }}
														@endif
													@endif
												@else
													{!! $value !!}
												@endif
											</td>
										</tr>
									@endif
								@endforeach

								{{-- Swift fields separated with visual separator --}}
								@if (isset($bank['swift']) && !is_null($bank['swift']) && $bank['swift'] !== '')
									<tr>
										<td colspan="2" class="border-b border-gray-300 p-2">
										</td>
									</tr>
									@foreach ($bank as $key => $value)
										@if(in_array($key, ['swift', 'swift_correspondent']) && !is_null($value) && $value !== '')
											<tr>
												<td class="border border-gray-300 p-1" width="120px">
													{{ ucwords(str_replace('_', ' ', $key)) }}
												</td>
												<td class="border border-gray-300 p-1 {{ $bankInfoWeight }}">
													{!! $value !!}
												</td>
											</tr>
										@endif
									@endforeach
									<tr>
										<td colspan="2" class="border-t border-gray-300 p-2">
										</td>
									</tr>
								@endif

								{{-- Handle custom_columns (MySQL JSON) --}}
								@if(isset($bank['custom_columns']) && !empty($bank['custom_columns']))
									@php
										$customColumns = is_string($bank['custom_columns']) ? json_decode($bank['custom_columns'], true) : $bank['custom_columns'];
									@endphp
									@if(is_array($customColumns))
										@foreach($customColumns as $label => $field)
											@if(is_array($field) && isset($field['value']) && !empty($field['value']))
												<tr>
													<td class="border border-gray-300 p-1" width="120px">
														{{ ucwords(str_replace('_', ' ', $label)) }}
													</td>
													<td class="border border-gray-300 p-1 {{ $bankInfoWeight }}">
														@if(isset($field['type']) && $field['type'] === 'richtext')
															{!! $field['value'] !!}
														@else
															{{ $field['value'] }}
														@endif
													</td>
												</tr>
											@endif
										@endforeach
									@endif
								@endif
							</table>
						</div>
					@endforeach
				</div>
			@else
				{{-- Stacked Layout: Vertical --}}
				<div class="space-y-3">
					@foreach ($banks as $bank)
						<div class="{{ $bankInfoBorder ? 'border border-gray-300' : '' }} {{ $bankInfoTextColorClass }}" style="background-color: {{ $bankInfoBg }}">
							<table class="w-full text-xs-compact {{ $bankInfoDecor }} border-collapse">
								{{-- Regular fields (excluding Swift) --}}
								@foreach ($bank as $key => $value)
									@if(!in_array($key, ['id', 'company_id', 'is_default', 'include_in_invoice', 'custom_columns', 'swift', 'swift_correspondent']) && !is_null($value) && $value !== '')
										<tr>
											<td class="border border-gray-300 p-1 bg-gray-50" width="150px">
												{{ ucwords(str_replace('_', ' ', $key)) }}
											</td>
											<td class="border border-gray-300 p-1 {{ $bankInfoWeight }}">
												@if(str_contains($key, 'address') && str_contains($value, '<'))
													{!! strip_tags($value, '<p><br><strong><b><em><i><u>') !!}
												@elseif(is_array($value))
													{{-- Handle custom columns --}}
													@if(isset($value['type']) && isset($value['value']))
														@if($value['type'] === 'richtext')
															{!! strip_tags($value['value'], '<p><br><strong><b><em><i><u>') !!}
														@else
															{{ $value['value'] }}
														@endif
													@endif
												@else
													{!! $value !!}
												@endif
											</td>
										</tr>
									@endif
								@endforeach

								{{-- Swift fields separated with visual separator --}}
								@if (isset($bank['swift']) && !is_null($bank['swift']) && $bank['swift'] !== '')
									<tr>
										<td colspan="2" class="border border-gray-300 p-1">
										</td>
									</tr>
									@foreach ($bank as $key => $value)
										@if(in_array($key, ['swift', 'swift_correspondent']) && !is_null($value) && $value !== '')
											<tr>
												<td class="border border-gray-300 p-1" width="120px">
													{{ ucwords(str_replace('_', ' ', $key)) }}
												</td>
												<td class="border border-gray-300 p-1 {{ $bankInfoWeight }}">
													{!! $value !!}
												</td>
											</tr>
										@endif
									@endforeach
								@endif

								{{-- Handle custom_columns (MySQL JSON) --}}
								@if(isset($bank['custom_columns']) && !empty($bank['custom_columns']))
									@php
										$customColumns = is_string($bank['custom_columns']) ? json_decode($bank['custom_columns'], true) : $bank['custom_columns'];
									@endphp
									<tr>
										<td colspan="2" class="border border-gray-300 p-1">
										</td>
									</tr>
									@if(is_array($customColumns))
										@foreach($customColumns as $label => $field)
											@if(is_array($field) && isset($field['value']) && !empty($field['value']))
												<tr>
													<td class="border border-gray-300 p-1 bg-gray-50" width="150px">
														{{ ucwords(str_replace('_', ' ', $label)) }}
													</td>
													<td class="border border-gray-300 p-1 {{ $bankInfoWeight }}">
														@if(isset($field['type']) && $field['type'] === 'richtext')
															{!! $field['value'] !!}
														@else
															{{ $field['value'] }}
														@endif
													</td>
												</tr>
											@endif
										@endforeach
									@endif
								@endif
							</table>
						</div>
					@endforeach
				</div>
			@endif
		@endif
		@if($bankInfoLayout === 'stacked')
			@if ($bankColumnSize == 1)
				<div class="grid grid-cols-1 md:grid-cols-2 gap-3">
					@foreach ($banks as $bank)
						<div class="{{ $bankInfoDecor }} {{ $bankInfoTextColorClass }} {{ $bankInfoBorder || $bankInfoBg !== 'transparent' ? 'p-2 rounded' : '' }}"  style="background-color: {{ $bankInfoBg }}; {{ $bankInfoBorder ? 'border:1px solid #D1D5DB;' : '' }}">
							{{-- Regular fields (excluding Swift) --}}
							@foreach ($bank as $key => $value)
								@if(in_array($key, ['bank_name']) && !is_null($value) && $value !== '')
									<h6 class="h6 mb-2 border-b border-gray-300 py-2 font-bold uppercase">{{ $value }}</h3>
								@endif
							@endforeach
							<div class="space-y-2 text-xs-compact">
								@foreach ($bank as $key => $value)
									@if(!in_array($key, ['id', 'company_id', 'is_default', 'include_in_invoice', 'custom_columns', 'bank_name','swift', 'swift_correspondent']) && !is_null($value) && $value !== '')
										<div class="flex flex-col items-start">
											<span class="">{{ ucwords(str_replace('_', ' ', $key)) }}:</span>
											<span class="{{ $bankInfoWeight }}">{!! $value !!}</span>
										</div>
									@endif
								@endforeach

								{{-- Swift fields separated --}}
								@if (isset($bank['swift']) && !is_null($bank['swift']) && $bank['swift'] !== '')
									<div class="py-3 space-y-1">
										@foreach ($bank as $key => $value)
											@if(in_array($key, ['swift', 'swift_correspondent']) && !is_null($value) && $value !== '')
												<div class="">
													<div class="flex flex-col items-start">
														<span class="col-span-1">{{ ucwords(str_replace('_', ' ', $key)) }}:</span>
														<span class="col-start-2 col-end-6 {{ $bankInfoWeight }} ">{!! $value !!}</span>
													</div>
												</div>
											@endif
										@endforeach
									</div>
								@endif

								{{-- Handle custom_columns (MySQL JSON) --}}
								@if(isset($bank['custom_columns']) && !empty($bank['custom_columns']))
									@php
										$customColumns = is_string($bank['custom_columns']) ? json_decode($bank['custom_columns'], true) : $bank['custom_columns'];
									@endphp
									@if(is_array($customColumns))
										@foreach($customColumns as $label => $field)
											@if(is_array($field) && isset($field['value']) && !empty($field['value']))
												<div class="flex flex-col items-start mt-1">
													<span class="">{{ ucwords(str_replace('_', ' ', $label)) }}:</span>
													<span class="{{ $bankInfoWeight }}">
														@if(isset($field['type']) && $field['type'] === 'richtext')
															{!! $field['value'] !!}
														@else
															{{ $field['value'] }}
														@endif
													</span>
												</div>
											@endif
										@endforeach
									@endif
								@endif
							</div>
						</div>
					@endforeach
				</div>
			@else
				<div class="space-y-2">
					@foreach ($banks as $bank)
						<div class="text-xs-compact space-y-1 {{ $bankInfoDecor }} {{ $bankInfoTextColorClass }} {{ $bankInfoBorder || $bankInfoBg !== 'transparent' ? 'p-2 rounded' : '' }}"  style="background-color: {{ $bankInfoBg }}; {{ $bankInfoBorder ? 'border:1px solid #D1D5DB;' : '' }}">
							{{-- Regular fields (excluding Swift) --}}
							@foreach ($bank as $key => $value)
								@if(in_array($key, ['bank_name']) && !is_null($value) && $value !== '')
									<h6 class="h6 mb-2 border-b border-gray-300 font-bold uppercase">{{ $value }}</h3>
								@endif
							@endforeach
							@foreach ($bank as $key => $value)
								@if(!in_array($key, ['id', 'company_id', 'is_default', 'include_in_invoice', 'custom_columns', 'bank_name','swift', 'swift_correspondent']) && !is_null($value) && $value !== '')
									<div class="flex flex-col items-start">
										<span class="">{{ ucwords(str_replace('_', ' ', $key)) }}:</span>
										<span class="{{ $bankInfoWeight }}">{!! $value !!}</span>
									</div>
								@endif
							@endforeach

							{{-- Swift fields separated --}}
							@if (isset($bank['swift']) && !is_null($bank['swift']) && $bank['swift'] !== '')
								<div class="mt-1 space-y-1">
									@foreach ($bank as $key => $value)
										@if(in_array($key, ['swift', 'swift_correspondent']) && !is_null($value) && $value !== '')
											<div class="flex flex-col items-start">
												<span class="col-span-1">{{ ucwords(str_replace('_', ' ', $key)) }}:</span>
												<span class="col-start-2 col-end-6 {{ $bankInfoWeight }} ">{!! $value !!}</span>
											</div>
										@endif
									@endforeach
								</div>
							@endif

							{{-- Handle custom_columns (MySQL JSON) --}}
							@if(isset($bank['custom_columns']) && !empty($bank['custom_columns']))
								@php
									$customColumns = is_string($bank['custom_columns']) ? json_decode($bank['custom_columns'], true) : $bank['custom_columns'];
								@endphp
								@if(is_array($customColumns))
									<div class="grid grid-cols-2 items-start ">
										@foreach($customColumns as $label => $field)
											@if(is_array($field) && isset($field['value']) && !empty($field['value']))
												<div class="flex flex-col items-start mt-1">
													<span class="">{{ ucwords(str_replace('_', ' ', $label)) }}:</span>
													<span class="{{ $bankInfoWeight }}">
														@if(isset($field['type']) && $field['type'] === 'richtext')
															{!! $field['value'] !!}
														@else
															{{ $field['value'] }}
														@endif
													</span>
												</div>
											@endif
										@endforeach
									</div>
								@endif
							@endif
						</div>
					@endforeach
				</div>
			@endif
		@endif
		@if($bankInfoLayout === 'grid')
			@if ($bankColumnSize == 1)
				<div class="grid grid-cols-1 md:grid-cols-2 gap-3">
					@foreach ($banks as $bank)
						<div class="text-xs-compact space-y-2 {{ $bankInfoDecor }} {{ $bankInfoTextColorClass }} {{ $bankInfoBorder || $bankInfoBg !== 'transparent' ? 'p-2 rounded' : '' }}"  style="background-color: {{ $bankInfoBg }}; {{ $bankInfoBorder ? 'border:1px solid #D1D5DB;' : '' }}">
							{{-- Regular fields (excluding Swift) --}}
							@foreach ($bank as $key => $value)
								@if(!in_array($key, ['id', 'company_id', 'is_default', 'include_in_invoice', 'custom_columns', 'swift', 'swift_correspondent']) && !is_null($value) && $value !== '')
									<div class="grid grid-cols-5 items-start">
										<span class="col-span-1">{{ ucwords(str_replace('_', ' ', $key)) }}:</span>
										<span class="col-start-2 col-end-6 {{ $bankInfoWeight }} ">{!! $value !!}</span>
									</div>
								@endif
							@endforeach

							{{-- Swift fields separated --}}
							@if (isset($bank['swift']) && !is_null($bank['swift']) && $bank['swift'] !== '')
								<div class="py-3 space-y-1">
									@foreach ($bank as $key => $value)
										@if(in_array($key, ['swift', 'swift_correspondent']) && !is_null($value) && $value !== '')
											<div class="">
												<div class="grid grid-cols-5 items-start">
													<span class="col-span-1">{{ ucwords(str_replace('_', ' ', $key)) }}:</span>
													<span class="col-start-2 col-end-6 {{ $bankInfoWeight }} ">{!! $value !!}</span>
												</div>
											</div>
										@endif
									@endforeach
								</div>
							@endif

							{{-- Handle custom_columns (MySQL JSON) --}}
							@if(isset($bank['custom_columns']) && !empty($bank['custom_columns']))
								@php
									$customColumns = is_string($bank['custom_columns']) ? json_decode($bank['custom_columns'], true) : $bank['custom_columns'];
								@endphp
								@if(is_array($customColumns))
									@foreach($customColumns as $label => $field)
										@if(is_array($field) && isset($field['value']) && !empty($field['value']))
											<div class="grid grid-cols-5 items-start mt-1">
												<span class="col-span-1">{{ ucwords(str_replace('_', ' ', $label)) }}:</span>
												<span class="col-start-2 col-end-6 {{ $bankInfoWeight }}">
													@if(isset($field['type']) && $field['type'] === 'richtext')
														{!! $field['value'] !!}
													@else
														{{ $field['value'] }}
													@endif
												</span>
											</div>
										@endif
									@endforeach
								@endif
							@endif
						</div>
					@endforeach
				</div>
			@else
				<div class="space-y-2">
					@foreach ($banks as $bank)
						<div class="text-xs-compact space-y-2 {{ $bankInfoDecor }} {{ $bankInfoTextColorClass }} {{ $bankInfoBorder || $bankInfoBg !== 'transparent' ? 'p-2 rounded' : '' }}" style="background-color: {{ $bankInfoBg }}; {{ $bankInfoBorder ? 'border:1px solid #D1D5DB;' : '' }}">
							{{-- Regular fields (excluding Swift) --}}
							@foreach ($bank as $key => $value)
								@if(!in_array($key, ['id', 'company_id', 'is_default', 'include_in_invoice', 'custom_columns', 'swift', 'swift_correspondent']) && !is_null($value) && $value !== '')
									<div class="grid grid-cols-5 items-start">
										<span class="col-start-1 col-end-2">
											{{ ucwords(str_replace('_', ' ', $key)) }}:
										</span>
										<span class="col-start-2 col-end-6 {{ $bankInfoWeight }} ">
											{!! $value !!}
										</span>
									</div>
								@endif
							@endforeach

							{{-- Swift fields separated --}}
							@if (isset($bank['swift']) && !is_null($bank['swift']) && $bank['swift'] !== '')
								<div class="py-3 space-y-1">
									@foreach ($bank as $key => $value)
										@if(in_array($key, ['swift', 'swift_correspondent']) && !is_null($value) && $value !== '')
											<div>
												<div class="grid grid-cols-5 items-start">
													<span class="col-start-1 col-end-2">
														{{ ucwords(str_replace('_', ' ', $key)) }}:
													</span>
													<span class="col-start-2 col-end-6 {{ $bankInfoWeight }} ">
														{!! $value !!}
													</span>
												</div>
											</div>
										@endif
									@endforeach
								</div>
							@endif

							{{-- Handle custom_columns (MySQL JSON) --}}
							@if(isset($bank['custom_columns']) && !empty($bank['custom_columns']))
								@php
									$customColumns = is_string($bank['custom_columns']) ? json_decode($bank['custom_columns'], true) : $bank['custom_columns'];
								@endphp
								@if(is_array($customColumns))
									@foreach($customColumns as $label => $field)
										@if(is_array($field) && isset($field['value']) && !empty($field['value']))
											<div class="grid grid-cols-5 items-start mt-1">
												<span class="col-start-1 col-end-2">{{ ucwords(str_replace('_', ' ', $label)) }}:</span>
												<span class="col-start-2 col-end-6 {{ $bankInfoWeight }} ">
													@if(isset($field['type']) && $field['type'] === 'richtext')
														{!! $field['value'] !!}
													@else
														{{ $field['value'] }}
													@endif
												</span>
											</div>
										@endif
									@endforeach
								@endif
							@endif
						</div>
					@endforeach
				</div>
			@endif
		@endif
	</div>
@endif
