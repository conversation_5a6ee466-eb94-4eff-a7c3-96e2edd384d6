<?php

namespace App\Filament\Admin\Widgets;

use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Illuminate\Support\Facades\Auth;

class ShortcutKeyWidget extends BaseWidget
{
	protected int | string | array $columnSpan = '1';

	protected static string $view = 'filament.admin.widgets.shortcut-key-widget';
	// public static function canView(): bool
    // {
    //     return Auth::user()->hasAnyRole(['admin', 'Super Admin']);
    // }
}
