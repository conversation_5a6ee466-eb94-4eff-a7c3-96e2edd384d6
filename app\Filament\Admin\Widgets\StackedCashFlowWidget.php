<?php

namespace App\Filament\Admin\Widgets;

use App\Models\Company;
use App\Models\CompanyDepositSummary;
use App\Models\Order;
use Carbon\Carbon;
use Filament\Forms\Components\Select;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Number;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class StackedCashFlowWidget extends BaseWidget
{
	// protected static ?int $sort = 2;
	protected int | string | array $columnSpan = 3;

	protected static string $view = 'filament.admin.widgets.stacked-cashflow-widget';

	public function getViewData(): array
	{
		return Cache::remember('dashboard.view-data.summary', now()->addMinutes(5), function () {
			$now = now('Asia/Jakarta');
			$thisWeekStart = $now->copy()->startOfWeek();
			$thisWeekEnd = $now->copy()->endOfWeek();

			$lastWeekStart = $now->copy()->subWeek()->startOfWeek();
			$lastWeekEnd = $now->copy()->subWeek()->endOfWeek();

			$thisMonth = $now->month;
			$thisYear = $now->year;

			$grouped = Order::withoutGlobalScope('order')
				->select([
					'currency_id',
					DB::raw('COUNT(*) as orderCount'),
					DB::raw('SUM(total) as orderTotal'),
					DB::raw('SUM(rates * total) as totalPurchase'),
					DB::raw('SUM(sell_rates * total) as totalSell'),
					DB::raw('SUM(charges) as totalCharges'),
					DB::raw('SUM(((sell_rates - rates) * total) + charges) as totalProfit'),
				])
				->with('currency:id,name,symbol')
				->groupBy('currency_id')
				->get()
				->mapWithKeys(function ($item) {
					$name = $item->currency->name ?? 'Unknown';
					return [
						$name => [
							'symbol' => $item->currency->symbol ?? '',
							'orderCount' => $item->orderCount,
							'orderTotal' => $item->orderTotal,
							'totalPurchase' => $item->totalPurchase,
							'totalSell' => $item->totalSell,
							'totalCharges' => $item->totalCharges,
							'totalProfit' => $item->totalProfit,
						]
					];
				});


			$sumPurchase = fn($query) => $query->sum(DB::raw('rates * total'));

			$lastWeekPurchase = $sumPurchase(
				Order::whereBetween('order_date', [$lastWeekStart, $lastWeekEnd])
			);

			$thisWeekPurchase = $sumPurchase(
				Order::whereBetween('order_date', [$thisWeekStart, $thisWeekEnd])
			);

			$thisMonthPurchase = $sumPurchase(
				Order::whereYear('order_date', $thisYear)
					->whereMonth('order_date', $thisMonth)
			);

			$mostTradedKey = $grouped->sortByDesc('totalSell')->keys()->first();
			$mostTraded = $grouped[$mostTradedKey] ?? [];

			$mostTransactionedKey = $grouped->sortByDesc('orderCount')->keys()->first();
			$mostTransactioned = $grouped[$mostTransactionedKey] ?? [];
			$totalBalance = Cache::remember('dashboard.total-balance', now()->addMinutes(5), function () {
				return CompanyDepositSummary::sum('balance') ?? 0;
			});
			$cashFlowData = Cache::remember('dashboard.cashflow', now()->addMinutes(5), function () {
				return DB::table('company_depositos')
					->whereNull('deleted_at')
					->selectRaw('
                    SUM(CASE WHEN trx_type = "in" THEN amount ELSE 0 END) as total_in,
                    SUM(CASE WHEN trx_type = "out" THEN amount ELSE 0 END) as total_out,
                    SUM(CASE WHEN trx_type = "order" THEN amount ELSE 0 END) as total_order
                ')
					->first();
			});

			$cashFlowTotal = ($cashFlowData->total_in ?? 0) - ($cashFlowData->total_out ?? 0) - ($cashFlowData->total_order ?? 0);
			$lowBalanceThreshold = 1_000_000;
			$lowBalanceBanks = Cache::remember('dashboard.low-balance-banks', now()->addMinutes(5), function () use ($lowBalanceThreshold) {
				return CompanyDepositSummary::where('balance', '<', $lowBalanceThreshold)->count();
			});

			return [
				'leadingTradedCurrency' => [
					'name' => $mostTradedKey,
					'symbol' => $mostTraded['symbol'] ?? '',
					'orderCount' => $mostTraded['orderCount'] ?? 0,
					'totalSell' => $mostTraded['totalSell'] ?? 0,
				],
				'leadingTransactionedCurrency' => [
					'name' => $mostTransactionedKey,
					'symbol' => $mostTransactioned['symbol'] ?? '',
					'orderCount' => $mostTransactioned['orderCount'] ?? 0,
					'totalSell' => $mostTransactioned['totalSell'] ?? 0,
				],
				'transactionInsight' => [
					'lastWeekPurchase' => $lastWeekPurchase,
					'thisWeekPurchase' => $thisWeekPurchase,
					'thisMonthPurchase' => $thisMonthPurchase,
				],
				'totalBalance' => number_format($totalBalance, 2, ',', '.'),
				'cashFlowTotal' => number_format($cashFlowTotal, 2, ',', '.'),
				'lowBalanceBanks' => $lowBalanceBanks,
			];
		});
	}


	public static function canView(): bool
	{
		return Auth::user()->hasAnyRole(['admin', 'Super Admin']);
	}
	public static function canAccess(): bool
	{
		return Auth::user()->hasAnyRole(['admin', 'Super Admin']);
	}
}
