<?php

namespace App\Filament\Admin\Resources\CompanyResource\Pages;

use App\Filament\Admin\Resources\CompanyResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Filament\Support\Enums\Alignment;

class CreateCompany extends CreateRecord
{
    protected static string $resource = CompanyResource::class;
	public static string | Alignment $formActionsAlignment = Alignment::Right;


	protected static bool $canCreateAnother = false;
	protected function getCancelFormAction(): Actions\Action
    {
        return Actions\Action::make('cancel')
            ->label('Cancel')
            ->color('gray')
            ->action(fn () => $this->dispatch('close-modal'));
    }
	public function getRedirectUrl(): string
	{
		return '/admin';
	}
}
